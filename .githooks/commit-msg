#!/bin/sh
# Git hook to validate commit messages using commitlint
# To install: cp .githooks/commit-msg .git/hooks/commit-msg && chmod +x .git/hooks/commit-msg

# Check if commitlint is available
if ! command -v npx >/dev/null 2>&1; then
    echo "Warning: npx not found. Skipping commit message validation."
    exit 0
fi

# Check if commitlint config exists
if [ ! -f "commitlint.config.js" ]; then
    echo "Warning: commitlint.config.js not found. Skipping commit message validation."
    exit 0
fi

# Validate commit message
npx commitlint --edit "$1"
