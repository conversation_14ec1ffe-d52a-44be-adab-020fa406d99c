#!/bin/sh
# Git pre-commit hook to check for TODO/FIXME comments in source code
# To install: cp .githooks/pre-commit .git/hooks/pre-commit && chmod +x .git/hooks/pre-commit

# Check for TODO/FIXME comments in staged files
staged_files=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(js|ts|jsx|tsx)$' | grep '^src/')

if [ -n "$staged_files" ]; then
    # Check only staged files in src/ directory
    todo_files=$(echo "$staged_files" | xargs grep -l "TODO\|FIXME" 2>/dev/null || true)
    
    if [ -n "$todo_files" ]; then
        echo "❌ Found TODO/FIXME comments in staged files:"
        echo "$todo_files" | while read -r file; do
            echo "  $file:"
            grep -n "TODO\|FIXME" "$file" | sed 's/^/    /'
        done
        echo ""
        echo "Please resolve these TODO/FIXME comments before committing."
        echo "If you need to commit with these comments temporarily, use:"
        echo "  git commit --no-verify"
        exit 1
    else
        echo "✅ No TODO/FIXME comments found in staged source files"
    fi
fi

exit 0