import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { buildApp } from '../../src/app.js';
import configUtil from '../../src/utils/config.js';

// Mock configUtil
vi.mock('../../src/utils/config.js', () => ({
  default: {
    server: {
      port: 3000, host: '0.0.0.0', timezone: 'UTC',
    },
    mti: {
      baseUrl: "https://mti.hu", loginUrl: "https://mti.hu/bejelentkezes",
      categories: ["kozelet", "gazdasag", "vilag", "kultura", "sport", "en/english"],
      categoryContainerSelectors: { default: "selector" }
    },
    scraping: {
      interval: '0 * * * *', timeout: 30000, retries: 3,
      humanDelay: { min: 1, max: 2 }, jitterSeconds: { max: 1 },
      contentReadyTimeout: 10000, maxNewArticlesPerRun: 5,
    },
    feed: {
      version: 'https://jsonfeed.org/version/1.1',
      title: 'MTI News',
      homePageUrl: 'https://mti.hu',
      icon: 'https://static.mti.hu/logo.svg'
    },
    logging: { level: 'silent' },
    get: function(key) {
      const keys = key.split('.');
      let current = this.default;
      for (const k of keys) {
        if (current && typeof current === 'object' && k in current) {
          current = current[k];
        } else { return undefined; }
      }
      return current;
    }
  }
}));

// Mock ScraperService - Global scope for hoisting
const { mockScrapedArticles } = vi.hoisted(() => ({
  mockScrapedArticles: vi.fn()
}));
vi.mock('../../src/services/scraper.js', () => ({
  default: vi.fn().mockImplementation(() => ({
    scrapeCategories: mockScrapedArticles,
  })),
}));

// Mock AuthService - Global scope for hoisting
vi.mock('../../src/services/auth.js', () => ({
  default: vi.fn().mockImplementation(() => ({
    initBrowser: vi.fn().mockResolvedValue(undefined),
    maintainSession: vi.fn().mockResolvedValue(undefined),
    isBrowserInitialized: vi.fn().mockReturnValue(true),
    isAuthenticated: true,
    getPage: vi.fn().mockReturnValue({}), // Basic mock page
    cleanup: vi.fn().mockResolvedValue(undefined),
  })),
}));

describe('App Cache Logic - Category-Based Cache Initialization', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should initialize with category-based cache manager', async () => {
    const app = await buildApp({ test: true });
    expect(app.cacheManager).toBeDefined();
    expect(app.cacheManager.getAvailableCategories()).toEqual(['kozelet', 'gazdasag', 'vilag', 'kultura', 'sport', 'english']);

    // Check that each category cache is initialized
    const categories = app.cacheManager.getAvailableCategories();
    categories.forEach(category => {
      const categoryCache = app.cacheManager.getCategory(category);
      expect(categoryCache.items).toEqual([]);
      expect(categoryCache.category).toBe(category);
    });

    await app.close();
  });

  it('should allow category cache data to be updated independently', async () => {
    const app = await buildApp({ test: true });

    // Update kozelet category
    app.cacheManager.updateCategory('kozelet', [
      { id: 'test-kozelet', url: 'http://example.com/kozelet', title: 'Kozelet Article', tags: ['kozelet'] }
    ]);

    // Update gazdasag category
    app.cacheManager.updateCategory('gazdasag', [
      { id: 'test-gazdasag', url: 'http://example.com/gazdasag', title: 'Gazdasag Article', tags: ['gazdasag'] }
    ]);

    const kozeletCache = app.cacheManager.getCategory('kozelet');
    const gazdasagCache = app.cacheManager.getCategory('gazdasag');
    const vilagCache = app.cacheManager.getCategory('vilag');

    expect(kozeletCache.items).toHaveLength(1);
    expect(gazdasagCache.items).toHaveLength(1);
    expect(vilagCache.items).toHaveLength(0); // Unchanged

    expect(kozeletCache.items[0].title).toBe('Kozelet Article');
    expect(gazdasagCache.items[0].title).toBe('Gazdasag Article');

    await app.close();
  });

  it('should provide aggregated feed from all categories', async () => {
    const app = await buildApp({ test: true });

    // Add articles to different categories
    app.cacheManager.updateCategory('kozelet', [
      { id: 'test-kozelet', url: 'http://example.com/kozelet', title: 'Kozelet Article', tags: ['kozelet'], date_published: new Date().toISOString() }
    ]);
    app.cacheManager.updateCategory('sport', [
      { id: 'test-sport', url: 'http://example.com/sport', title: 'Sport Article', tags: ['sport'], date_published: new Date().toISOString() }
    ]);

    const aggregatedFeed = app.cacheManager.getAggregatedFeed();

    expect(aggregatedFeed.items).toHaveLength(2);
    expect(aggregatedFeed.title).toBe('MTI News');
    expect(aggregatedFeed.items.some(item => item.title === 'Kozelet Article')).toBe(true);
    expect(aggregatedFeed.items.some(item => item.title === 'Sport Article')).toBe(true);

    await app.close();
  });
});

describe('App Cache Logic - runScraperAndUpdateCacheInternal', () => {
  let app;

  beforeEach(async () => {
    mockScrapedArticles.mockReset();
    app = await buildApp({ test: true });
  });

  afterEach(async () => {
    if (app) {
      await app.close();
    }
    vi.clearAllMocks();
  });

  it('should add new articles to category caches', async () => {
    const articlesFromScraper = [
      { url: 'http://example.com/kozelet1', title: 'Kozelet Article 1', date_published: new Date(Date.now() - 10000).toISOString(), tags: ['kozelet'] },
      { url: 'http://example.com/sport1', title: 'Sport Article 1', date_published: new Date(Date.now() - 20000).toISOString(), tags: ['sport'] },
    ];
    mockScrapedArticles.mockResolvedValue(articlesFromScraper);

    // Check initial state
    expect(app.cacheManager.getCategory('kozelet').items).toHaveLength(0);
    expect(app.cacheManager.getCategory('sport').items).toHaveLength(0);

    await app.runScraperAndUpdateCache();

    expect(mockScrapedArticles).toHaveBeenCalledWith(expect.any(Set), null, configUtil.scraping.maxNewArticlesPerRun);

    // Check that articles were added to correct categories
    const kozeletCache = app.cacheManager.getCategory('kozelet');
    const sportCache = app.cacheManager.getCategory('sport');

    expect(kozeletCache.items).toHaveLength(1);
    expect(sportCache.items).toHaveLength(1);
    expect(kozeletCache.items[0].title).toBe('Kozelet Article 1');
    expect(sportCache.items[0].title).toBe('Sport Article 1');
    expect(kozeletCache.lastUpdated).not.toBeNull();
    expect(sportCache.lastUpdated).not.toBeNull();
  });

  it('should add new articles to existing category caches and sort by date', async () => {
    // Pre-populate some categories
    app.cacheManager.updateCategory('kozelet', [
      { url: 'http://example.com/old-kozelet', title: 'Old Kozelet Article', date_published: new Date(Date.now() - 300000).toISOString(), tags: ['kozelet'] }
    ]);
    app.cacheManager.updateCategory('sport', [
      { url: 'http://example.com/old-sport', title: 'Old Sport Article', date_published: new Date(Date.now() - 400000).toISOString(), tags: ['sport'] }
    ]);

    const articlesFromScraper = [
      { url: 'http://example.com/new-kozelet', title: 'New Kozelet Article', date_published: new Date(Date.now() - 10000).toISOString(), tags: ['kozelet'] },
      { url: 'http://example.com/new-sport', title: 'New Sport Article', date_published: new Date(Date.now() - 5000).toISOString(), tags: ['sport'] },
    ];
    mockScrapedArticles.mockResolvedValue(articlesFromScraper);

    await app.runScraperAndUpdateCache();

    const kozeletCache = app.cacheManager.getCategory('kozelet');
    const sportCache = app.cacheManager.getCategory('sport');

    expect(kozeletCache.items).toHaveLength(2); // 1 old + 1 new
    expect(sportCache.items).toHaveLength(2); // 1 old + 1 new

    // Check sorting (newest first)
    expect(kozeletCache.items[0].title).toBe('New Kozelet Article');
    expect(kozeletCache.items[1].title).toBe('Old Kozelet Article');
    expect(sportCache.items[0].title).toBe('New Sport Article');
    expect(sportCache.items[1].title).toBe('Old Sport Article');

    // Check that existing URLs were passed to scraper
    const expectedExistingUrls = new Set(['http://example.com/old-kozelet', 'http://example.com/old-sport']);
    expect(mockScrapedArticles).toHaveBeenCalledWith(expectedExistingUrls, null, configUtil.scraping.maxNewArticlesPerRun);
  });

  it('should not add duplicate articles if scraper returns an existing URL', async () => {
    // Pre-populate kozelet category
    app.cacheManager.updateCategory('kozelet', [
      { url: 'http://example.com/existing', title: 'Existing Article', date_published: new Date().toISOString(), tags: ['kozelet'] }
    ]);

    const articlesFromScraper = [
      { url: 'http://example.com/existing', title: 'Existing Article Updated', date_published: new Date(Date.now() + 1000).toISOString(), tags: ['kozelet'] },
      { url: 'http://example.com/newUnique', title: 'New Unique Article', date_published: new Date().toISOString(), tags: ['kozelet'] },
    ];
    mockScrapedArticles.mockResolvedValue(articlesFromScraper);
    await app.runScraperAndUpdateCache();

    const kozeletCache = app.cacheManager.getCategory('kozelet');
    expect(kozeletCache.items).toHaveLength(2);
    const existingArticleInCache = kozeletCache.items.find(item => item.url === 'http://example.com/existing');
    expect(existingArticleInCache.title).toBe('Existing Article Updated'); // Should be updated
  });

  it('should handle empty array from scraper without errors', async () => {
    // Pre-populate a category
    app.cacheManager.updateCategory('kozelet', [
      { url: 'http://example.com/1', title: 'Article 1', date_published: new Date().toISOString(), tags: ['kozelet'] }
    ]);

    const initialItems = [...app.cacheManager.getCategory('kozelet').items];
    mockScrapedArticles.mockResolvedValue([]);
    await app.runScraperAndUpdateCache();

    const kozeletCache = app.cacheManager.getCategory('kozelet');
    expect(kozeletCache.items).toEqual(initialItems); // Should remain unchanged
  });

  it('should handle scraper error and log it', async () => {
    const errorMessage = 'Scraper failed spectacularly';
    mockScrapedArticles.mockRejectedValue(new Error(errorMessage));
    await app.runScraperAndUpdateCache();

    // Should not crash and cache manager should remain functional
    expect(app.cacheManager.getStats().totalArticles).toBe(0);
  });
});
