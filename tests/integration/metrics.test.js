import { describe, it, expect, beforeAll, afterAll, vi } from 'vitest';
import { buildApp } from '../../src/app.js';

// Mock external dependencies to avoid network calls and browser automation
vi.mock('../../src/services/auth.js', () => ({
  default: class MockAuthService {
    constructor() {
      this.isAuthenticated = true;
      this.browserInitialized = true;
    }
    initBrowser = vi.fn().mockResolvedValue(undefined);
    maintainSession = vi.fn().mockResolvedValue(true);
    checkAuthStatus = vi.fn().mockResolvedValue(true);
    cleanup = vi.fn().mockResolvedValue(undefined);
    isBrowserInitialized = vi.fn().mockReturnValue(true);
    login = vi.fn().mockResolvedValue(true);
  }
}));

vi.mock('../../src/services/scraper.js', () => ({
  default: class MockScraperService {
    constructor() {}
    scrapeAndCache = vi.fn().mockResolvedValue({
      success: true,
      items: [
        {
          id: 'test-article-1',
          title: 'Test Article 1',
          date_published: '2025-06-05T10:00:00.000Z',
          tags: ['politics']
        }
      ]
    });
    cleanup = vi.fn().mockResolvedValue(undefined);
  }
}));

describe('Metrics Integration Tests', () => {
  let app;

  beforeAll(async () => {
    // Build the app with mocked services
    app = await buildApp();
    await app.ready();

    // Set up some mock cached data using the new CacheManager
    // The app should have a cacheManager instance from the mocked services
  });

  afterAll(async () => {
    if (app) {
      await app.close();
    }
  });

  describe('GET /metrics', () => {
    it('should return metrics with correct structure', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/metrics'
      });

      expect(response.statusCode).toBe(200);

      const metrics = JSON.parse(response.payload);
      
      // Check basic structure
      expect(metrics).toHaveProperty('status', 'ok');
      expect(metrics).toHaveProperty('timestamp');
      expect(metrics).toHaveProperty('uptime');
      expect(metrics).toHaveProperty('memory');
      expect(metrics).toHaveProperty('cache');
      expect(metrics).toHaveProperty('statistics');
      expect(metrics).toHaveProperty('version', '1.2.0');

      // Check uptime structure
      expect(metrics.uptime).toHaveProperty('seconds');
      expect(metrics.uptime).toHaveProperty('human');
      expect(typeof metrics.uptime.seconds).toBe('number');
      expect(typeof metrics.uptime.human).toBe('string');

      // Check memory structure (now nested under process)
      expect(metrics.memory).toHaveProperty('process');
      expect(metrics.memory.process).toHaveProperty('rss');
      expect(metrics.memory.process).toHaveProperty('heapTotal');
      expect(metrics.memory.process).toHaveProperty('heapUsed');
      expect(metrics.memory.process).toHaveProperty('external');
      expect(metrics.memory).toHaveProperty('cache');

      // Check cache structure (new category-based structure)
      expect(metrics.cache).toHaveProperty('type', 'category-based');
      expect(metrics.cache).toHaveProperty('totalArticles');
      expect(metrics.cache).toHaveProperty('totalCategories');
      expect(metrics.cache).toHaveProperty('availableCategories');
      expect(metrics.cache).toHaveProperty('timeSinceLastUpdate');
      expect(metrics.cache).toHaveProperty('categories');
      expect(metrics.cache).toHaveProperty('limits');

      // Check statistics structure (new structure)
      expect(metrics.statistics).toHaveProperty('recentArticles');
      expect(metrics.statistics).toHaveProperty('categoryBreakdown');
    });

    it('should return cached data information', async () => {
      // Mock cached data using CacheManager
      app.cacheManager.updateCategory('kozelet', [
        {
          id: 'test-id',
          url: 'http://example.com/test',
          title: 'Integration Test Article',
          date_published: new Date().toISOString(),
          tags: ['kozelet']
        }
      ]);

      const response = await app.inject({
        method: 'GET',
        url: '/metrics'
      });

      expect(response.statusCode).toBe(200);

      const metrics = JSON.parse(response.payload);

      // Should include our mocked cached data
      expect(metrics.cache.totalArticles).toBe(1);
      expect(metrics.statistics.recentArticles).toHaveLength(1);
      expect(metrics.statistics.recentArticles[0].title).toBe('Integration Test Article');
    });

    it('should return valid JSON', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/metrics'
      });

      expect(response.statusCode).toBe(200);
      expect(response.headers['content-type']).toContain('application/json');
      
      // Should not throw when parsing
      expect(() => JSON.parse(response.payload)).not.toThrow();
    });

    it('should have consistent timestamp format', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/metrics'
      });

      const metrics = JSON.parse(response.payload);
      
      // Should be a valid ISO string
      expect(() => new Date(metrics.timestamp)).not.toThrow();
      expect(new Date(metrics.timestamp).toISOString()).toBe(metrics.timestamp);
    });
  });
});
