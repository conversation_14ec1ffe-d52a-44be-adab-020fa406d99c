import { describe, it, expect, beforeAll, afterAll, vi, beforeEach } from 'vitest';
import { buildApp } from '../../src/app.js'; // Import buildApp
import configUtil from '../../src/utils/config.js'; // To access the original config for comparison

// Mock scraperService to control its behavior during tests
// We need to get the actual path to scraper.js relative to this test file
// Vitest's vi.mock is hoisted, so direct import path is fine.
vi.mock('../../src/services/auth.js', () => {
  return {
    default: class MockAuthService {
      constructor() {
        this.isAuthenticated = true; // Assume authenticated for tests unless specified
        this.browserInitialized = true;
        vi.fn().mockImplementation(() => this.logger.info('MockAuthService constructor called'));
      }
      initBrowser = vi.fn().mockResolvedValue(undefined);
      maintainSession = vi.fn().mockResolvedValue(true);
      checkAuthStatus = vi.fn().mockResolvedValue(true);
      getPage = vi.fn().mockReturnValue({ /* mock page object if needed by scraper */ });
      cleanup = vi.fn().mockResolvedValue(undefined);
      isBrowserInitialized = vi.fn().mockReturnValue(true); // Assume browser is initialized
      // Add any other methods used by app.js or other services
      login = vi.fn().mockResolvedValue(true); // Add mock for login
    }
  };
});

vi.mock('../../src/services/scraper.js', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    // Keep other exports if any, or just mock the class
    ...actual,
    default: class MockScraperService {
      constructor(authService, config, logger) {
        this.logger = logger || console; // Use console if logger not provided
        this.logger.info('MockScraperService initialized');
      }
      // Mock the scrapeCategories method
      scrapeCategories = vi.fn().mockResolvedValue([]); // Default to empty array
    }
  };
});


describe('Fastify Server Integration', () => {
  let app; // This will be our fastify instance from buildApp
  let mockAuthServiceInstance;
  let mockScraperServiceInstance;

  beforeAll(async () => {
    // Set a specific node environment for testing if not already set
    process.env.NODE_ENV = 'test'; 
    app = await buildApp({isTest: true}); // Pass a testConfig to indicate test environment

    // Access the decorated instances from the app BEFORE calling ready()
    mockAuthServiceInstance = app.authServiceInstance;
    mockScraperServiceInstance = app.scraperServiceInstance;

    // Ensure the mocks on the instances are working as expected
    // These checks help confirm that the mocking and decoration setup is correct.
    if (!mockAuthServiceInstance || !mockAuthServiceInstance.initBrowser) {
        throw new Error('MockAuthServiceInstance not correctly initialized or decorated on app.');
    }
    if (!mockScraperServiceInstance || !mockScraperServiceInstance.scrapeCategories) {
        throw new Error('MockScraperServiceInstance not correctly initialized or decorated on app.');
    }
    
    await app.ready(); // Ensure all plugins are loaded AFTER accessing decorators
  });

  beforeEach(async () => {
    // Reset mocks on the actual instances used by the app
    if (mockScraperServiceInstance && mockScraperServiceInstance.scrapeCategories) {
      mockScraperServiceInstance.scrapeCategories.mockClear();
      // Default to empty array for GET /feed tests unless overridden
      mockScraperServiceInstance.scrapeCategories.mockResolvedValue([]); 
    }
    if (mockAuthServiceInstance && mockAuthServiceInstance.maintainSession) {
      mockAuthServiceInstance.maintainSession.mockClear().mockResolvedValue(true);
      // Ensure isAuthenticated is true for the mockAuthServiceInstance by default for tests
      // This is tricky if the constructor of MockAuthService sets it, as that runs once.
      // We might need to be able to set this on the instance if a test requires it to be false.
      // For now, the mock class constructor sets it to true.
    }
    
    // Reset cache state in the app using the decorated reference
    if (app.cachedFeedData) {
      app.cachedFeedData.items = [];
      app.cachedFeedData.lastUpdated = null;
    }
  });


  afterAll(async () => {
    if (app) {
      await app.close();
    }
  });

  describe('GET /health', () => {
    it('should return health status from the actual app', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/health'
      });
      expect(response.statusCode).toBe(200);
      const payload = JSON.parse(response.payload);
      expect(payload.status).toBe('healthy');
      expect(payload.version).toBe('1.0.0');
    });
  });

  describe('GET /feed', () => {
    it('should return 503 if cache is empty and not yet updated', async () => {
      // beforeEach ensures cache is empty and mockScraperServiceInstance.scrapeCategories returns []
      
      const response = await app.inject({
        method: 'GET',
        url: '/feed'
      });
      expect(response.statusCode).toBe(503);
      const payload = JSON.parse(response.payload);
      expect(payload.message).toBe("Feed data is not yet available. Please try again later.");
    });

    it('should return data from cache after scraper run', async () => {
      const mockArticles = [{
        id: '1',
        title: 'Test Article 1',
        url: 'http://example.com/1',
        tags: ['kozelet'],
        date_published: new Date().toISOString()
      }];
      // Configure the app's scraperService instance (mockScraperServiceInstance is app.scraperServiceInstance)
      mockScraperServiceInstance.scrapeCategories.mockResolvedValueOnce(mockArticles);

      // Trigger the app's cache update mechanism
      await app.runScraperAndUpdateCache(); // This function is decorated on 'app' for test purposes

      const response = await app.inject({
        method: 'GET',
        url: '/feed'
      });
      expect(response.statusCode).toBe(200);
      const payload = JSON.parse(response.payload);
      expect(payload.items).toHaveLength(1);
      expect(payload.items[0].title).toBe('Test Article 1');
      expect(payload.title).toBe('MTI News');
    });

    // it('should handle scraper errors and continue serving feed', async () => {
    //   const errorMessage = "Scraping failed badly for test";
      
    //   // Ensure cache is completely empty
    //   app.cachedFeedData.items = [];
    //   app.cachedFeedData.lastUpdated = null;
      
    //   mockScraperServiceInstance.scrapeCategories.mockRejectedValueOnce(new Error(errorMessage));
      
    //   // Trigger the app's cache update mechanism, which should catch the error
    //   await app.runScraperAndUpdateCache();

    //   // Since cache is empty and scraper failed, should return 503
    //   const response = await app.inject({
    //     method: 'GET',
    //     url: '/feed'
    //   });
      
    //   expect(response.statusCode).toBe(503);
    //   const payload = JSON.parse(response.payload);
    //   expect(payload.message).toBe("Feed data is not yet available. Please try again later.");
    // });
  });

  // Keep CORS and general error handling tests if they are still relevant
  describe('CORS', () => {
    it('should include CORS headers', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/health',
        headers: {
          origin: 'http://localhost:3000' // Example origin
        }
      });
      // Fastify's default CORS behavior with origin:true might be just '*'
      // or reflect the origin. This depends on the exact CORS setup.
      // Let's check if the header exists.
      expect(response.headers['access-control-allow-origin']).toBeDefined();
    });
  });

  describe('Error handling', () => {
    it('should return 404 for non-existent routes', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/non-existent-route-for-sure'
      });
      expect(response.statusCode).toBe(404);
    });
  });
});
