<!DOCTYPE html>
<html lang="hu">
<head>
    <meta charset="UTF-8">
    <title><PERSON><PERSON><PERSON> (Teszt)</title>
</head>
<body>
    <div>
        <!-- Mocking the structure for date extraction -->
        <span>Valami más szöveg</span>
        <span class="py-0.75">2024. január 15. hétfő 10:30</span> 
        <!-- The scraper looks for the second span with py-0.75 -->
    </div>
    <div class="svelte-1tcypa1"> <!-- This class is targeted by the scraper for content -->
        <p>Ez egy teszt összefoglaló a kategória oldalon.</p> <!-- This should be removed by the cleaner if it matches summary -->
        <p>Ez a cikk <strong>tényleges</strong> tartalma, ami hosszabb.</p>
        <p>Még egy bekezdés a <span>tartalomhoz</span>.</p>
    </div>
</body>
</html>
