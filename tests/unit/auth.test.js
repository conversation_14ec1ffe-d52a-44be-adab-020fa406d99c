import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import AuthService from '../../src/services/auth.js';

// Mock dependencies
vi.mock('playwright', () => {
  const mockPage = {
    goto: vi.fn().mockImplementation(async (url) => {
      // Simulate successful navigation - update the URL
      mockPage.url.mockReturnValue(url);
      return { status: () => 200 };
    }),
    locator: vi.fn().mockReturnValue({
      first: vi.fn().mockReturnValue({
        isVisible: vi.fn().mockResolvedValue(true),
        click: vi.fn(),
        fill: vi.fn()
      })
    }),
    waitForSelector: vi.fn(),
    waitForLoadState: vi.fn(),
    url: vi.fn().mockReturnValue('https://mti.hu/dashboard'),
    context: vi.fn().mockReturnValue({
      cookies: vi.fn().mockResolvedValue([
        { name: 'session_token', value: 'test_token' }
      ])
    }),
    setViewportSize: vi.fn(),
    close: vi.fn()
  };

  return {
    chromium: {
      launch: vi.fn().mockResolvedValue({
        newContext: vi.fn().mockResolvedValue({
          newPage: vi.fn().mockResolvedValue(mockPage)
        }),
        close: vi.fn()
      })
    }
  };
});

vi.mock('../../src/utils/logger.js', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    default: {
      info: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn()
    }
  };
});

vi.mock('../../src/utils/config.js', () => ({
  default: {
    mti: {
      baseUrl: 'https://mti.hu',
      loginUrl: 'https://mti.hu/bejelentkezes'
    }
  }
}));

vi.mock('../../src/utils/helpers.js', () => ({
  randomDelay: vi.fn().mockResolvedValue(undefined)
}));

// Mock environment variables
vi.mock('../../src/utils/env.js', () => ({}));


describe('Authentication Service', () => {
  let authService;

  beforeEach(() => {
    authService = new AuthService();
    vi.clearAllMocks();
  });

  afterEach(async () => {
    await authService.cleanup();
  });

  describe('constructor', () => {
    it('should initialize with default state', () => {
      expect(authService.isAuthenticated).toBe(false);
      expect(authService.browser).toBeNull();
      expect(authService.page).toBeNull();
    });
  });

  describe('initBrowser', () => {
    it('should initialize browser and page', async () => {
      const result = await authService.initBrowser();
      
      expect(result).toBeTruthy();
      expect(authService.browser).toBeTruthy();
      expect(authService.page).toBeTruthy();
    });
  });

  describe('login', () => {
    it('should perform login with valid credentials', async () => {
      const result = await authService.login('<EMAIL>', 'password123');
      
      expect(result).toBe(true);
      expect(authService.isAuthenticated).toBe(true);
    });

    it('should fail with authentication error when login fails', async () => {
      // Mock failed login scenario - no session cookie
      const { chromium } = await import('playwright');
      const mockBrowser = await chromium.launch();
      const mockContext = await mockBrowser.newContext();
      const mockPage = await mockContext.newPage();
      
      mockPage.context.mockReturnValue({
        cookies: vi.fn().mockResolvedValue([]) // No session cookies
      });
      mockPage.url.mockReturnValue('https://mti.hu/bejelentkezes'); // Still on login page

      await expect(authService.login('<EMAIL>', 'wrongpassword')).rejects.toThrow();
      expect(authService.isAuthenticated).toBe(false);
    });
  });

  describe('checkAuthStatus', () => {
    it('should return true when authenticated', async () => {
      await authService.initBrowser();
      authService.isAuthenticated = true;
      
      const result = await authService.checkAuthStatus();
      
      expect(result).toBe(true);
    });

    it('should return false when not authenticated', async () => {
      const result = await authService.checkAuthStatus();
      
      expect(result).toBe(false);
    });

    it('should detect session expiry and return false', async () => {
      await authService.initBrowser();
      authService.isAuthenticated = true;
      
      // Mock redirect to login page (session expired)
      authService.page.goto.mockImplementationOnce(async (url) => {
        authService.page.url.mockReturnValue('https://mti.hu/bejelentkezes');
        return { status: () => 200 };
      });
      
      const result = await authService.checkAuthStatus();
      
      expect(result).toBe(false);
      expect(authService.isAuthenticated).toBe(false);
    });
  });

  describe('maintainSession', () => {
    it('should login when not authenticated', async () => {
      // Set environment variables for the test
      process.env.MTI_EMAIL = '<EMAIL>';
      process.env.MTI_PASSWORD = 'password123';
      
      // Initialize browser first, then modify the mocks
      await authService.initBrowser();
      
      // Mock successful login scenario
      authService.page.url.mockReturnValue('https://mti.hu/dashboard'); // Not login page
      authService.page.context().cookies.mockResolvedValue([
        { name: 'session_token', value: 'test_token' }
      ]);
      
      const result = await authService.maintainSession();
      
      expect(result).toBe(true);
      expect(authService.isAuthenticated).toBe(true);
    });

    it('should check session validity when authenticated', async () => {
      await authService.initBrowser();
      authService.isAuthenticated = true;
      authService.lastLoginTime = new Date(Date.now() - 15 * 60 * 1000); // 15 minutes ago
      
      process.env.MTI_EMAIL = '<EMAIL>';
      process.env.MTI_PASSWORD = 'password123';
      
      // Mock URL to show we're NOT on login page (session is valid)
      authService.page.url.mockReturnValue('https://mti.hu/kozelet');
      
      const result = await authService.maintainSession();
      
      expect(result).toBe(true);
    });

    it('should throw error when credentials missing', async () => {
      delete process.env.MTI_EMAIL;
      delete process.env.MTI_PASSWORD;
      
      await expect(authService.maintainSession()).rejects.toThrow('MTI_EMAIL and MTI_PASSWORD environment variables are required');
    });
  });

  describe('cleanup', () => {
    it('should close browser and reset state', async () => {
      await authService.initBrowser();
      authService.isAuthenticated = true;
      
      await authService.cleanup();
      
      expect(authService.browser).toBeNull();
      expect(authService.page).toBeNull();
      expect(authService.isAuthenticated).toBe(false);
    });

    it('should handle cleanup when already clean', async () => {
      // Should not throw when already cleaned up
      await expect(authService.cleanup()).resolves.toBeUndefined();
    });
  });
});
