import { readFileSync } from 'fs';
import { describe, expect, it } from 'vitest';

describe('Versioning and Changelog System', () => {
  describe('Package.json', () => {
    it('should have semantic versioning format', () => {
      const packageJson = JSON.parse(readFileSync('package.json', 'utf8'));
      const version = packageJson.version;
      
      // Semantic versioning regex: MAJOR.MINOR.PATCH
      const semverRegex = /^\d+\.\d+\.\d+$/;
      expect(version).toMatch(semverRegex);
    });

    it('should have versioning scripts', () => {
      const packageJson = JSON.parse(readFileSync('package.json', 'utf8'));
      const scripts = packageJson.scripts;
      
      expect(scripts).toHaveProperty('release');
      expect(scripts).toHaveProperty('release:minor');
      expect(scripts).toHaveProperty('release:major');
      expect(scripts).toHaveProperty('release:patch');
      expect(scripts).toHaveProperty('release:dry-run');
      expect(scripts).toHaveProperty('version:check');
    });

    it('should have required devDependencies for versioning', () => {
      const packageJson = JSON.parse(readFileSync('package.json', 'utf8'));
      const devDeps = packageJson.devDependencies;
      
      expect(devDeps).toHaveProperty('standard-version');
      expect(devDeps).toHaveProperty('@commitlint/cli');
      expect(devDeps).toHaveProperty('@commitlint/config-conventional');
    });
  });

  describe('CHANGELOG.md', () => {
    it('should exist and follow Keep a Changelog format', () => {
      const changelog = readFileSync('CHANGELOG.md', 'utf8');
      
      expect(changelog).toContain('# Changelog');
      expect(changelog).toContain('Keep a Changelog');
      expect(changelog).toContain('Semantic Versioning');
      expect(changelog).toContain('[1.0.0]');
    });

    it('should have proper version sections', () => {
      const changelog = readFileSync('CHANGELOG.md', 'utf8');
      
      // Should have version headers
      expect(changelog).toMatch(/## \[\d+\.\d+\.\d+\]/);
      
      // Should have date format
      expect(changelog).toMatch(/\d{4}-\d{2}-\d{2}/);
      
      // Should have sections like Added, Changed, Removed, etc.
      expect(changelog).toContain('### Added');
      expect(changelog).toContain('### Changed');
    });
  });

  describe('Configuration Files', () => {
    it('should have .versionrc.json configuration', () => {
      const versionrc = JSON.parse(readFileSync('.versionrc.json', 'utf8'));
      
      expect(versionrc).toHaveProperty('types');
      expect(versionrc).toHaveProperty('commitUrlFormat');
      expect(versionrc).toHaveProperty('compareUrlFormat');
      expect(versionrc.types).toBeInstanceOf(Array);
      
      // Check for required commit types
      const types = versionrc.types.map(t => t.type);
      expect(types).toContain('feat');
      expect(types).toContain('fix');
      expect(types).toContain('docs');
    });

    it('should have commitlint configuration', () => {
      // Check if commitlint.config.js exists
      expect(() => {
        readFileSync('commitlint.config.js', 'utf8');
      }).not.toThrow();
    });
  });

  describe('GitHub Workflows', () => {
    it('should have release workflow', () => {
      const releaseWorkflow = readFileSync('.github/workflows/release.yml', 'utf8');

      expect(releaseWorkflow).toContain('name: Release');
      expect(releaseWorkflow).toContain('npm run release');
      expect(releaseWorkflow).toContain('create-release');
    });

    it('should have updated CI workflow with commitlint', () => {
      const ciWorkflow = readFileSync('.github/workflows/node.js.yml', 'utf8');
      
      expect(ciWorkflow).toContain('commitlint');
      expect(ciWorkflow).toContain('fetch-depth: 0');
    });
  });
});
