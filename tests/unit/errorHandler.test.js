import { describe, it, expect, vi, beforeEach } from 'vitest';
import { withErrorHandling, globalErrorHandler, withAsyncErrorHandling, getHttpStatusForError } from '../../src/utils/errorHandler.js';
import { AuthenticationError, NetworkError, ValidationError, MTIError } from '../../src/utils/errors.js';

describe('Error Handler Utilities', () => {
  let mockRequest, mockReply, mockLogger;

  beforeEach(() => {
    mockLogger = {
      error: vi.fn(),
      info: vi.fn(),
      warn: vi.fn()
    };

    mockRequest = {
      method: 'GET',
      url: '/test',
      headers: { 'user-agent': 'test-agent' },
      ip: '127.0.0.1',
      server: {
        log: mockLogger
      }
    };

    mockReply = {
      status: vi.fn().mockReturnThis(),
      send: vi.fn().mockReturnThis()
    };
  });

  describe('withErrorHandling', () => {
    it('should handle successful controller execution', async () => {
      const testController = async (request, reply) => {
        return { success: true };
      };

      const wrappedController = withErrorHandling(testController);
      const result = await wrappedController(mockRequest, mockReply);

      expect(result).toEqual({ success: true });
      expect(mockLogger.error).not.toHaveBeenCalled();
    });

    it('should handle MTI custom errors properly', async () => {
      const testController = async (request, reply) => {
        throw new ValidationError('Invalid input', { field: 'category' });
      };

      const wrappedController = withErrorHandling(testController);
      await wrappedController(mockRequest, mockReply);

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.objectContaining({
          err: expect.any(ValidationError),
          method: 'GET',
          url: '/test',
          errorName: 'ValidationError',
          errorCode: 'VALIDATION_ERROR'
        }),
        expect.stringContaining('Request failed: Invalid input')
      );

      expect(mockReply.status).toHaveBeenCalledWith(400);
      expect(mockReply.send).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Request failed',
          message: 'Invalid input',
          code: 'VALIDATION_ERROR'
        })
      );
    });

    it('should handle unexpected errors properly', async () => {
      const testController = async (request, reply) => {
        throw new Error('Unexpected error');
      };

      const wrappedController = withErrorHandling(testController);
      await wrappedController(mockRequest, mockReply);

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.objectContaining({
          err: expect.any(Error),
          method: 'GET',
          url: '/test',
          errorName: 'Error',
          errorCode: 'UNKNOWN_ERROR'
        }),
        expect.stringContaining('Unexpected error in request: Unexpected error')
      );

      expect(mockReply.status).toHaveBeenCalledWith(500);
      expect(mockReply.send).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Internal server error',
          message: 'An unexpected error occurred',
          code: 'INTERNAL_ERROR'
        })
      );
    });
  });

  describe('globalErrorHandler', () => {
    it('should handle MTI errors in global context', () => {
      const error = new AuthenticationError('Login failed');
      
      globalErrorHandler(error, mockRequest, mockReply);

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.objectContaining({
          err: error,
          method: 'GET',
          url: '/test',
          errorName: 'AuthenticationError'
        }),
        expect.stringContaining('Global error handler: Login failed')
      );

      expect(mockReply.status).toHaveBeenCalledWith(401);
    });

    it('should handle unexpected errors in global context', () => {
      const error = new Error('System error');
      
      globalErrorHandler(error, mockRequest, mockReply);

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.objectContaining({
          err: error,
          errorName: 'Error'
        }),
        expect.stringContaining('Global error handler: Unexpected error - System error')
      );

      expect(mockReply.status).toHaveBeenCalledWith(500);
    });
  });

  describe('withAsyncErrorHandling', () => {
    it('should execute function successfully without error', async () => {
      const testFunction = async (value) => {
        return value * 2;
      };

      const wrappedFunction = withAsyncErrorHandling(testFunction, {
        logger: mockLogger,
        context: 'test operation'
      });

      const result = await wrappedFunction(5);
      expect(result).toBe(10);
      expect(mockLogger.error).not.toHaveBeenCalled();
    });

    it('should handle and re-throw MTI errors', async () => {
      const testFunction = async () => {
        throw new NetworkError('Connection failed');
      };

      const wrappedFunction = withAsyncErrorHandling(testFunction, {
        logger: mockLogger,
        context: 'network operation'
      });

      await expect(wrappedFunction()).rejects.toThrow('Connection failed');
      
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.objectContaining({
          err: expect.any(NetworkError),
          context: 'network operation',
          errorName: 'NetworkError',
          errorCode: 'NETWORK_ERROR'
        }),
        'network operation failed: Connection failed'
      );
    });

    it('should handle unexpected errors', async () => {
      const testFunction = async () => {
        throw new Error('Unexpected error');
      };

      const wrappedFunction = withAsyncErrorHandling(testFunction, {
        logger: mockLogger,
        context: 'async operation'
      });

      await expect(wrappedFunction()).rejects.toThrow('Unexpected error');
      
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.objectContaining({
          err: expect.any(Error),
          context: 'async operation',
          errorName: 'Error',
          errorCode: 'UNKNOWN_ERROR'
        }),
        'async operation failed with unexpected error: Unexpected error'
      );
    });

    it('should exit process when exitOnError is true', async () => {
      const originalExit = process.exit;
      process.exit = vi.fn();

      const testFunction = async () => {
        throw new Error('Critical error');
      };

      const wrappedFunction = withAsyncErrorHandling(testFunction, {
        exitOnError: true,
        logger: mockLogger,
        context: 'critical operation'
      });

      await expect(wrappedFunction()).rejects.toThrow('Critical error');
      expect(process.exit).toHaveBeenCalledWith(1);

      // Restore original process.exit
      process.exit = originalExit;
    });
  });

  describe('HTTP status code mapping', () => {
    it('should map error types to correct HTTP status codes', () => {
      expect(getHttpStatusForError(new AuthenticationError('Auth failed'))).toBe(401);
      expect(getHttpStatusForError(new NetworkError('Network failed'))).toBe(502);
      expect(getHttpStatusForError(new ValidationError('Validation failed'))).toBe(400);
      expect(getHttpStatusForError(new MTIError('Generic error'))).toBe(500);
    });
  });
});