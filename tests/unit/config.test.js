import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';

describe('Configuration Module', () => {
  let originalEnv;

  beforeEach(() => {
    // Save original environment variables
    originalEnv = { ...process.env };
    
    // Clear config module from cache
    vi.resetModules();
  });

  afterEach(() => {
    // Restore original environment variables
    process.env = originalEnv;
  });

  it('should load default configuration', async () => {
    const { config } = await import('../../src/utils/config.js');
    
    expect(config).toBeDefined();
    expect(config.server).toBeDefined();
    expect(config.server.port).toBe(3000);
    expect(config.server.host).toBe('0.0.0.0');
    expect(config.mti).toBeDefined();
    expect(config.mti.baseUrl).toBe('https://mti.hu');
    expect(config.mti.categories).toHaveLength(6);
  });

  it('should override port from environment variable', async () => {
    process.env.PORT = '4000';
    
    const { config } = await import('../../src/utils/config.js');
    
    expect(config.server.port).toBe(4000);
  });

  it('should override MTI credentials from environment variables', async () => {
    process.env.MTI_EMAIL = '<EMAIL>';
    process.env.MTI_PASSWORD = 'testpassword';
    
    const { config } = await import('../../src/utils/config.js');
    
    expect(config.mti.email).toBe('<EMAIL>');
    expect(config.mti.password).toBe('testpassword');
  });

  it('should override scraping interval from environment variable', async () => {
    process.env.SCRAPE_INTERVAL = '0 */30 * * * *';
    
    const { config } = await import('../../src/utils/config.js');
    
    expect(config.scraping.interval).toBe('0 */30 * * * *');
  });

  it('should have all required MTI categories', async () => {
    const { config } = await import('../../src/utils/config.js');
    
    const expectedCategories = [
      'kozelet',
      'gazdasag',
      'vilag',
      'kultura',
      'sport',
      'en/english'
    ];
    
    expect(config.mti.categories).toEqual(expectedCategories);
  });

  it('should have scraping configuration with human delay settings', async () => {
    const { config } = await import('../../src/utils/config.js');
    
    expect(config.scraping.humanDelay).toBeDefined();
    expect(config.scraping.humanDelay.min).toBe(1000);
    expect(config.scraping.humanDelay.max).toBe(10000);
  });

  it('should have feed configuration with default values', async () => {
    const { config } = await import('../../src/utils/config.js');
    
    expect(config.feed).toBeDefined();
    expect(config.feed.version).toBe('https://jsonfeed.org/version/1.1');
    expect(config.feed.title).toBe('MTI News');
    expect(config.feed.homePageUrl).toBe('https://mti.hu');
    expect(config.feed.icon).toBe('https://static.mti.hu/logo.svg');
  });

  it('should override feed configuration from environment variables', async () => {
    process.env.FEED_TITLE = 'Custom News Title';
    process.env.FEED_HOME_PAGE_URL = 'https://example.com';
    process.env.MAX_NEW_ARTICLES_PER_RUN = '50';

    const { config } = await import('../../src/utils/config.js');

    expect(config.feed.title).toBe('Custom News Title');
    expect(config.feed.homePageUrl).toBe('https://example.com');
    expect(config.scraping.maxNewArticlesPerRun).toBe(50);
  });
});
