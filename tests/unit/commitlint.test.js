import { describe, it, expect } from 'vitest';
import { execSync } from 'child_process';

describe('Commitlint Configuration', () => {
  it('should validate a few key conventional commit messages', () => {
    // Test a valid message
    expect(() => {
      execSync(`echo "feat: add new feature" | npx commitlint`, { 
        cwd: process.cwd(),
        stdio: 'pipe' 
      });
    }).not.toThrow();

    // Test another valid message
    expect(() => {
      execSync(`echo "fix: resolve authentication bug" | npx commitlint`, { 
        cwd: process.cwd(),
        stdio: 'pipe' 
      });
    }).not.toThrow();
  });

  it('should reject a few key invalid commit messages', () => {
    // Test invalid message (no type)
    expect(() => {
      execSync(`echo "Add new feature" | npx commitlint`, { 
        cwd: process.cwd(),
        stdio: 'pipe' 
      });
    }).toThrow();

    // Test invalid message (empty)
    expect(() => {
      execSync(`echo "" | npx commitlint`, { 
        cwd: process.cwd(),
        stdio: 'pipe' 
      });
    }).toThrow();
  });

  it('should have commitlint.config.js with correct rules', () => {
    const config = require('../../commitlint.config.js');
    
    expect(config.default).toBeDefined();
    expect(config.default.extends).toContain('@commitlint/config-conventional');
    expect(config.default.rules).toBeDefined();
    expect(config.default.rules['type-enum']).toBeDefined();
    expect(config.default.rules['subject-empty']).toEqual([2, 'never']);
    expect(config.default.rules['type-empty']).toEqual([2, 'never']);
  });

  it('should have all required commit types in configuration', () => {
    const config = require('../../commitlint.config.js');
    const typeEnum = config.default.rules['type-enum'];
    const allowedTypes = typeEnum[2];
    
    const requiredTypes = [
      'feat', 'fix', 'docs', 'style', 'refactor', 
      'perf', 'test', 'build', 'ci', 'chore', 'revert'
    ];
    
    requiredTypes.forEach(type => {
      expect(allowedTypes).toContain(type);
    });
  });
});