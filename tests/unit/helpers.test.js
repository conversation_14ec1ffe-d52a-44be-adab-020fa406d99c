import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  randomDelay,
  generateArticleId,
  toISOString,
  isValidUrl,
  cleanText
} from '../../src/utils/helpers.js';

describe('Helper Functions', () => {
  describe('randomDelay', () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    it('should return a promise', () => {
      const result = randomDelay(100, 200);
      expect(result).toBeInstanceOf(Promise);
    });

    it('should delay for a time within the specified range', async () => {
      const min = 1000;
      const max = 2000;
      
      // Mock Math.random to return a predictable value
      const mockRandom = vi.spyOn(Math, 'random').mockReturnValue(0.5);
      
      // The delay should be exactly in the middle (min + 0.5 * (max - min))
      const expectedDelay = min + 0.5 * (max - min + 1);
      
      const delayPromise = randomDelay(min, max);
      
      // Fast-forward time enough to complete the delay
      vi.advanceTimersByTime(expectedDelay);
      
      await expect(delayPromise).resolves.toBeUndefined();
      
      // Restore original Math.random
      mockRandom.mockRestore();
    });

    it('should use default values when no parameters provided', async () => {
      const delayPromise = randomDelay();
      
      // Fast-forward time by default max
      vi.advanceTimersByTime(10000);
      
      await expect(delayPromise).resolves.toBeUndefined();
    });
  });

  describe('generateArticleId', () => {
    it('should generate a unique ID from URL and title', () => {
      const url = 'https://mti.hu/news/test-article-123';
      const title = 'Test Article Title';
      const result = generateArticleId(url, title);
      
      expect(result).toBe('test-article-123-testarticletitle');
    });

    it('should handle empty URL', () => {
      const url = '';
      const title = 'Test Title';
      const result = generateArticleId(url, title);
      
      expect(result).toBe('-testtitle');
    });

    it('should handle special characters in title', () => {
      const url = 'https://mti.hu/news/article';
      const title = 'Tést Ârtìclé with Spëcîál Çháracters!@#$%';
      const result = generateArticleId(url, title);
      
      expect(result).toBe('article-tstrtclwithspchlchrc');
    });

    it('should truncate long titles', () => {
      const url = 'https://mti.hu/news/article';
      const title = 'This is a very long article title that should be truncated';
      const result = generateArticleId(url, title);
      
      expect(result).toBe('article-thisisaverylongartic');
    });
  });

  describe('toISOString', () => {
    it('should convert Date object to ISO string', () => {
      const date = new Date('2025-06-04T10:30:00Z');
      const result = toISOString(date);
      
      expect(result).toBe('2025-06-04T10:30:00.000Z');
    });

    it('should convert date string to ISO string', () => {
      const dateString = '2025-06-04T10:30:00Z';
      const result = toISOString(dateString);
      
      expect(result).toBe('2025-06-04T10:30:00.000Z');
    });

    it('should return current date ISO string when no date provided', () => {
      const before = new Date().toISOString();
      const result = toISOString();
      const after = new Date().toISOString();
      
      expect(result >= before && result <= after).toBe(true);
    });

    it('should return current date ISO string when null provided', () => {
      const before = new Date().toISOString();
      const result = toISOString(null);
      const after = new Date().toISOString();
      
      expect(result >= before && result <= after).toBe(true);
    });
  });

  describe('isValidUrl', () => {
    it('should return true for valid HTTP URLs', () => {
      expect(isValidUrl('http://example.com')).toBe(true);
      expect(isValidUrl('https://mti.hu')).toBe(true);
      expect(isValidUrl('https://mti.hu/news/article')).toBe(true);
    });

    it('should return true for valid URLs with query parameters', () => {
      expect(isValidUrl('https://example.com?param=value')).toBe(true);
      expect(isValidUrl('https://example.com/path?param1=value1&param2=value2')).toBe(true);
    });

    it('should return false for invalid URLs', () => {
      expect(isValidUrl('not-a-url')).toBe(false);
      expect(isValidUrl('just-text')).toBe(false);
      expect(isValidUrl('')).toBe(false);
      expect(isValidUrl('htp://invalid-protocol.com')).toBe(false);
    });

    it('should return false for null or undefined', () => {
      expect(isValidUrl(null)).toBe(false);
      expect(isValidUrl(undefined)).toBe(false);
    });
  });

  describe('cleanText', () => {
    it('should clean and normalize text', () => {
      const dirtyText = '  This   is    a   test  \n\t  text  ';
      const result = cleanText(dirtyText);
      
      expect(result).toBe('This is a test text');
    });

    it('should handle empty string', () => {
      expect(cleanText('')).toBe('');
    });

    it('should handle null and undefined', () => {
      expect(cleanText(null)).toBe('');
      expect(cleanText(undefined)).toBe('');
    });

    it('should replace line breaks and tabs with spaces', () => {
      const text = 'Line 1\nLine 2\rLine 3\tTabbed';
      const result = cleanText(text);
      
      expect(result).toBe('Line 1 Line 2 Line 3 Tabbed');
    });

    it('should collapse multiple spaces into single space', () => {
      const text = 'Multiple     spaces      here';
      const result = cleanText(text);
      
      expect(result).toBe('Multiple spaces here');
    });
  });
});
