import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { getMetrics } from '../../src/controllers/metrics.js';

describe('Metrics Controller', () => {
  let mockRequest;
  let mockReply;
  let originalUptime;
  let originalMemoryUsage;

  beforeEach(() => {
    // Mock process.uptime
    originalUptime = process.uptime;
    process.uptime = vi.fn().mockReturnValue(3661); // 1 hour, 1 minute, 1 second

    // Mock process.memoryUsage
    originalMemoryUsage = process.memoryUsage;
    process.memoryUsage = vi.fn().mockReturnValue({
      rss: 50 * 1024 * 1024, // 50 MB
      heapTotal: 30 * 1024 * 1024, // 30 MB
      heapUsed: 20 * 1024 * 1024, // 20 MB
      external: 5 * 1024 * 1024 // 5 MB
    });

    // Mock Date to have consistent timestamps
    vi.useFakeTimers();
    vi.setSystemTime(new Date('2025-06-05T12:00:00.000Z'));

    // Setup mock request and reply
    mockReply = {};
    mockRequest = {
      headers: {
        'user-agent': 'test-user-agent'
      },
      method: 'GET',
      url: '/metrics',
      ip: '127.0.0.1',
      server: {
        cacheManager: {
          getStats: vi.fn().mockReturnValue({
            totalArticles: 0,
            categories: {},
            limits: {
              global: 1500,
              categories: {
                kozelet: 300,
                gazdasag: 250,
                vilag: 300,
                kultura: 200,
                sport: 200,
                english: 250
              }
            }
          }),
          getMemoryUsage: vi.fn().mockReturnValue({
            totalMB: 0,
            totalBytes: 0,
            categories: {}
          }),
          getAggregatedFeed: vi.fn().mockReturnValue({
            items: [],
            lastUpdated: null
          }),
          getAvailableCategories: vi.fn().mockReturnValue(['kozelet', 'gazdasag', 'vilag', 'kultura', 'sport', 'english'])
        }
      }
    };
  });

  afterEach(() => {
    vi.useRealTimers();
    process.uptime = originalUptime;
    process.memoryUsage = originalMemoryUsage;
    vi.restoreAllMocks();
  });

  describe('getMetrics', () => {
    it('should return basic metrics when no cached data is available', async () => {
      const result = await getMetrics(mockRequest, mockReply);

      expect(result).toEqual({
        status: 'ok',
        timestamp: '2025-06-05T12:00:00.000Z',
        uptime: {
          seconds: 3661,
          human: '1h 1m 1s'
        },
        memory: {
          process: {
            rss: 50,
            heapTotal: 30,
            heapUsed: 20,
            external: 5
          },
          cache: {
            totalMB: 0,
            totalBytes: 0,
            categories: {}
          }
        },
        cache: {
          type: 'category-based',
          totalArticles: 0,
          totalCategories: 0,
          availableCategories: ['kozelet', 'gazdasag', 'vilag', 'kultura', 'sport', 'english'],
          timeSinceLastUpdate: null,
          categories: {},
          limits: {
            global: 1500,
            categories: {
              kozelet: 300,
              gazdasag: 250,
              vilag: 300,
              kultura: 200,
              sport: 200,
              english: 250
            }
          }
        },
        statistics: {
          recentArticles: [],
          categoryBreakdown: []
        },
        version: '1.2.0'
      });
    });

    it('should return metrics with cached feed data', async () => {
      const mockArticles = [
        {
          id: 'article-1',
          title: 'Test Article 1',
          date_published: '2025-06-05T10:00:00.000Z',
          tags: ['politics']
        },
        {
          id: 'article-2',
          title: 'Test Article 2',
          date_published: '2025-06-05T09:00:00.000Z',
          tags: ['sports']
        },
        {
          id: 'article-3',
          title: 'Test Article 3',
          date_published: '2025-06-05T08:00:00.000Z',
          tags: ['politics']
        }
      ];

      // Update the mock cacheManager to return data
      mockRequest.server.cacheManager.getStats.mockReturnValue({
        totalArticles: 3,
        categories: {
          politics: { count: 2, utilizationPercent: 67, lastUpdated: '2025-06-05T11:30:00.000Z' },
          sports: { count: 1, utilizationPercent: 33, lastUpdated: '2025-06-05T11:30:00.000Z' }
        },
        limits: {
          global: 1500,
          categories: {
            kozelet: 300,
            gazdasag: 250,
            vilag: 300,
            kultura: 200,
            sport: 200,
            english: 250
          }
        }
      });

      mockRequest.server.cacheManager.getAggregatedFeed.mockReturnValue({
        items: mockArticles,
        lastUpdated: '2025-06-05T11:30:00.000Z'
      });

      const result = await getMetrics(mockRequest, mockReply);

      expect(result.cache.totalArticles).toBe(3);
      expect(result.cache.timeSinceLastUpdate).toEqual({
        seconds: 1800, // 30 minutes
        human: '30 minutes ago'
      });

      expect(result.statistics.categoryBreakdown).toEqual([
        { category: 'politics', count: 2, utilizationPercent: 67, lastUpdated: '2025-06-05T11:30:00.000Z' },
        { category: 'sports', count: 1, utilizationPercent: 33, lastUpdated: '2025-06-05T11:30:00.000Z' }
      ]);
      expect(result.cache.totalCategories).toBe(2);
      expect(result.statistics.recentArticles).toHaveLength(3);
    });

    it('should handle cached data with no articles', async () => {
      // Reset mocks to return empty data
      mockRequest.server.cacheManager.getStats.mockReturnValue({
        totalArticles: 0,
        categories: {},
        limits: {
          global: 1500,
          categories: {
            kozelet: 300,
            gazdasag: 250,
            vilag: 300,
            kultura: 200,
            sport: 200,
            english: 250
          }
        }
      });

      mockRequest.server.cacheManager.getAggregatedFeed.mockReturnValue({
        items: [],
        lastUpdated: '2025-06-05T11:00:00.000Z'
      });

      const result = await getMetrics(mockRequest, mockReply);

      expect(result.cache.totalArticles).toBe(0);
      expect(result.cache.totalCategories).toBe(0);
    });

    it('should limit recent articles to 5 items', async () => {
      const mockItems = Array.from({ length: 10 }, (_, i) => ({
        id: `article-${i + 1}`,
        title: `Test Article ${i + 1}`,
        date_published: `2025-06-05T${10 + i}:00:00.000Z`,
        tags: ['general']
      }));

      mockRequest.server.cacheManager.getStats.mockReturnValue({
        totalArticles: 10,
        categories: {
          general: { count: 10, utilizationPercent: 100, lastUpdated: '2025-06-05T11:30:00.000Z' }
        },
        limits: {
          global: 1500,
          categories: {
            kozelet: 300,
            gazdasag: 250,
            vilag: 300,
            kultura: 200,
            sport: 200,
            english: 250
          }
        }
      });

      mockRequest.server.cacheManager.getAggregatedFeed.mockReturnValue({
        items: mockItems,
        lastUpdated: '2025-06-05T11:30:00.000Z'
      });

      const result = await getMetrics(mockRequest, mockReply);

      expect(result.statistics.recentArticles).toHaveLength(5);
      expect(result.statistics.recentArticles[0].id).toBe('article-1');
      expect(result.statistics.recentArticles[4].id).toBe('article-5');
    });

    it('should handle articles without tags', async () => {
      const mockArticles = [
        {
          id: 'article-1',
          title: 'Article without tags',
          date_published: '2025-06-05T10:00:00.000Z'
        },
        {
          id: 'article-2',
          title: 'Article with empty tags',
          date_published: '2025-06-05T09:00:00.000Z',
          tags: []
        },
        {
          id: 'article-3',
          title: 'Article with tags',
          date_published: '2025-06-05T08:00:00.000Z',
          tags: ['sports']
        }
      ];

      mockRequest.server.cacheManager.getStats.mockReturnValue({
        totalArticles: 3,
        categories: {
          sports: { count: 1, utilizationPercent: 33, lastUpdated: '2025-06-05T11:30:00.000Z' }
        },
        limits: {
          global: 1500,
          categories: {
            kozelet: 300,
            gazdasag: 250,
            vilag: 300,
            kultura: 200,
            sport: 200,
            english: 250
          }
        }
      });

      mockRequest.server.cacheManager.getAggregatedFeed.mockReturnValue({
        items: mockArticles,
        lastUpdated: '2025-06-05T11:30:00.000Z'
      });

      const result = await getMetrics(mockRequest, mockReply);

      expect(result.statistics.categoryBreakdown).toEqual([
        { category: 'sports', count: 1, utilizationPercent: 33, lastUpdated: '2025-06-05T11:30:00.000Z' }
      ]);
      expect(result.cache.totalCategories).toBe(1);
    });

    it('should include cache memory usage information', async () => {
      mockRequest.server.cacheManager.getMemoryUsage.mockReturnValue({
        totalMB: 2.5,
        totalBytes: 2621440,
        categories: {
          kozelet: { sizeMB: 1.2, sizeBytes: 1258291 },
          sport: { sizeMB: 1.3, sizeBytes: 1363149 }
        }
      });

      const result = await getMetrics(mockRequest, mockReply);

      expect(result.memory.cache.totalMB).toBe(2.5);
      expect(result.memory.cache.totalBytes).toBe(2621440);
      expect(result.memory.cache.categories).toEqual({
        kozelet: { sizeMB: 1.2, sizeBytes: 1258291 },
        sport: { sizeMB: 1.3, sizeBytes: 1363149 }
      });
    });

    it('should format memory usage in MB with proper rounding', async () => {
      process.memoryUsage.mockReturnValue({
        rss: 51234567, // 48.86 MB
        heapTotal: 31234567, // 29.79 MB
        heapUsed: 21234567, // 20.25 MB
        external: 5234567 // 4.99 MB
      });

      const result = await getMetrics(mockRequest, mockReply);

      expect(result.memory.process.rss).toBe(48.86);
      expect(result.memory.process.heapTotal).toBe(29.79);
      expect(result.memory.process.heapUsed).toBe(20.25);
      expect(result.memory.process.external).toBe(4.99);
    });
  });

  describe('Helper Functions Integration', () => {
    it('should format uptime correctly for various durations', async () => {
      // Test different uptime scenarios
      const testCases = [
        { uptime: 30, expected: '30s' },
        { uptime: 90, expected: '1m 30s' },
        { uptime: 3661, expected: '1h 1m 1s' },
        { uptime: 86461, expected: '1d 1m 1s' },
        { uptime: 0, expected: '0s' }
      ];

      for (const testCase of testCases) {
        process.uptime.mockReturnValue(testCase.uptime);
        const result = await getMetrics(mockRequest, mockReply);
        expect(result.uptime.human).toBe(testCase.expected);
      }
    });

    it('should format time difference correctly for various periods', async () => {
      const testCases = [
        { minutesAgo: 0.5, expected: '30 seconds ago' },
        { minutesAgo: 1, expected: '1 minute ago' },
        { minutesAgo: 30, expected: '30 minutes ago' },
        { minutesAgo: 90, expected: '1 hour ago' },
        { minutesAgo: 1500, expected: '1 day ago' },
        { minutesAgo: 3000, expected: '2 days ago' }
      ];

      for (const testCase of testCases) {
        const lastUpdated = new Date(Date.now() - testCase.minutesAgo * 60 * 1000);

        mockRequest.server.cacheManager.getAggregatedFeed.mockReturnValue({
          items: [],
          lastUpdated: lastUpdated.toISOString()
        });

        const result = await getMetrics(mockRequest, mockReply);
        expect(result.cache.timeSinceLastUpdate.human).toBe(testCase.expected);
      }
    });
  });

  describe('Edge Cases', () => {
    it('should handle undefined cacheManager', async () => {
      mockRequest.server.cacheManager = undefined;

      // This should throw an error, so we need to catch it
      try {
        await getMetrics(mockRequest, mockReply);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).toContain('Cannot read properties of undefined');
      }
    });

    it('should handle empty cache data', async () => {
      // Reset mocks to return empty data (already set in beforeEach)
      const result = await getMetrics(mockRequest, mockReply);

      expect(result.cache.totalArticles).toBe(0);
      expect(result.statistics.recentArticles).toEqual([]);
    });

    it('should handle invalid lastUpdated date', async () => {
      mockRequest.server.cacheManager.getAggregatedFeed.mockReturnValue({
        items: [],
        lastUpdated: 'invalid-date'
      });

      const result = await getMetrics(mockRequest, mockReply);

      // Should not crash and should handle gracefully - invalid dates result in NaN
      expect(result.cache.timeSinceLastUpdate.seconds).toBeNaN();
      expect(result.cache.timeSinceLastUpdate.human).toContain('NaN');
    });
  });
});
