import { describe, expect, it } from 'vitest';
import { processEmbeddedContent } from '../../src/utils/embeddedContent.js';

describe('embeddedContent.js', () => {
  describe('processEmbeddedContent', () => {
    it('should handle null or undefined input', () => {
      expect(processEmbeddedContent(null)).toBeNull();
      expect(processEmbeddedContent(undefined)).toBeUndefined();
    });

    it('should handle non-string input', () => {
      expect(processEmbeddedContent(123)).toBe(123);
      expect(processEmbeddedContent([])).toEqual([]);
      expect(processEmbeddedContent({})).toEqual({});
    });

    it('should handle empty string', () => {
      expect(processEmbeddedContent('')).toBe('');
    });

    it('should process basic HTML without embedded content unchanged', () => {
      const html = '<p>Simple paragraph</p><div>Simple div</div>';
      const result = processEmbeddedContent(html);
      expect(result).toBe(html);
    });

    it('should process Facebook video iframe', () => {
      const html = `
        <div>
          <iframe src="https://www.facebook.com/plugins/video.php?height=476&href=https%3A%2F%2Fwww.facebook.com%2Freel%2F1357341088679930%2F&show_text=true&width=267&t=0" width="267" height="591"></iframe>
        </div>
      `;
      
      const result = processEmbeddedContent(html);
      
      expect(result).toContain('Facebook Video');
      expect(result).toContain('🎥 Videó megtekintése Facebookon');
      expect(result).toContain('https://www.facebook.com/reel/1357341088679930/');
      expect(result).not.toContain('<iframe');
    });

    it('should process Facebook post iframe', () => {
      const html = `
        <div>
          <iframe src="https://www.facebook.com/plugins/post.php?href=https%3A%2F%2Fwww.facebook.com%2Fsome-user%2Fposts%2F123456789&width=500"></iframe>
        </div>
      `;
      
      const result = processEmbeddedContent(html);
      
      expect(result).toContain('Facebook Poszt');
      expect(result).toContain('📱 Poszt megtekintése Facebookon');
      expect(result).toContain('https://www.facebook.com/some-user/posts/123456789');
      expect(result).not.toContain('<iframe');
    });

    it('should process YouTube embed iframe', () => {
      const html = `
        <div>
          <iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>
        </div>
      `;
      
      const result = processEmbeddedContent(html);
      
      expect(result).toContain('YouTube Videó');
      expect(result).toContain('▶️ Videó megtekintése YouTube-on');
      expect(result).toContain('https://www.youtube.com/watch?v=dQw4w9WgXcQ');
      expect(result).toContain('https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg');
      expect(result).not.toContain('<iframe');
    });

    it('should process YouTube short URL iframe', () => {
      const html = `
        <div>
          <iframe src="https://youtu.be/dQw4w9WgXcQ" width="560" height="315"></iframe>
        </div>
      `;
      
      const result = processEmbeddedContent(html);
      
      expect(result).toContain('YouTube Videó');
      expect(result).toContain('https://www.youtube.com/watch?v=dQw4w9WgXcQ');
      expect(result).not.toContain('<iframe');
    });

    it('should process Twitter iframe', () => {
      const html = `
        <div>
          <iframe src="https://platform.twitter.com/embed/index.html?tweet_id=123456789"></iframe>
        </div>
      `;
      
      const result = processEmbeddedContent(html);
      
      expect(result).toContain('Twitter Poszt');
      expect(result).toContain('🐦 Poszt megtekintése Twitteren');
      expect(result).not.toContain('<iframe');
    });

    it('should process Instagram iframe', () => {
      const html = `
        <div>
          <iframe src="https://www.instagram.com/p/ABC123/embed"></iframe>
        </div>
      `;
      
      const result = processEmbeddedContent(html);
      
      expect(result).toContain('Instagram Poszt');
      expect(result).toContain('📷 Poszt megtekintése Instagramon');
      expect(result).not.toContain('<iframe');
    });

    it('should process generic iframe with domain extraction', () => {
      const html = `
        <div>
          <iframe src="https://example.com/some/path/embed" width="500" height="300"></iframe>
        </div>
      `;
      
      const result = processEmbeddedContent(html);
      
      expect(result).toContain('Beágyazott tartalom');
      expect(result).toContain('example.com');
      expect(result).toContain('🔗 Tartalom megtekintése külön ablakban');
      expect(result).toContain('https://example.com/some/path/embed');
      expect(result).not.toContain('<iframe');
    });

    it('should handle iframe without src attribute', () => {
      const html = `
        <div>
          <p>Before iframe</p>
          <iframe width="500" height="300"></iframe>
          <p>After iframe</p>
        </div>
      `;
      
      const result = processEmbeddedContent(html);
      
      // Should not process iframe without src, just leave it as is
      expect(result).toContain('<iframe');
      expect(result).toContain('Before iframe');
      expect(result).toContain('After iframe');
    });

    it('should process multiple different iframes in same content', () => {
      const html = `
        <div>
          <p>Content with multiple embeds:</p>
          <iframe src="https://www.youtube.com/embed/abc123"></iframe>
          <p>Between iframes</p>
          <iframe src="https://www.facebook.com/plugins/video.php?href=https%3A%2F%2Fwww.facebook.com%2Fvideo%2F123"></iframe>
          <p>After iframes</p>
        </div>
      `;
      
      const result = processEmbeddedContent(html);
      
      expect(result).toContain('YouTube Videó');
      expect(result).toContain('Facebook Video');
      expect(result).toContain('Between iframes');
      expect(result).toContain('After iframes');
      expect(result).not.toContain('<iframe');
    });

    it('should remove social media script tags', () => {
      const html = `
        <div>
          <p>Content with scripts</p>
          <script src="https://platform.twitter.com/widgets.js"></script>
          <script src="https://www.instagram.com/embed.js"></script>
          <script src="https://connect.facebook.net/en_US/sdk.js"></script>
          <script src="https://example.com/safe-script.js"></script>
          <p>After scripts</p>
        </div>
      `;
      
      const result = processEmbeddedContent(html);
      
      // Should remove social media scripts but keep safe ones
      expect(result).not.toContain('platform.twitter.com');
      expect(result).not.toContain('instagram.com/embed.js');
      expect(result).not.toContain('connect.facebook.net');
      expect(result).toContain('example.com/safe-script.js'); // Safe script should remain
      expect(result).toContain('[Beágyazott közösségi média widget eltávolítva a jobb kompatibilitás érdekében]');
    });

    it('should replace object and embed tags', () => {
      const html = `
        <div>
          <p>Content with multimedia</p>
          <object data="https://example.com/video.swf" width="400" height="300">
            <param name="movie" value="https://example.com/video.swf">
          </object>
          <embed src="https://example.com/audio.mp3" width="300" height="50">
          <p>After multimedia</p>
        </div>
      `;
      
      const result = processEmbeddedContent(html);
      
      expect(result).not.toContain('<object');
      expect(result).not.toContain('<embed');
      expect(result).toContain('[Beágyazott multimédiás tartalom nem támogatott a hírcsatornában]');
      expect(result).toContain('After multimedia');
    });

    it('should handle complex nested HTML structure', () => {
      const html = `
        <article>
          <h1>Article Title</h1>
          <div class="content">
            <p>Introduction paragraph</p>
            <div class="media-section">
              <iframe src="https://www.youtube.com/embed/test123" width="560" height="315"></iframe>
            </div>
            <p>Middle paragraph</p>
            <div class="social-section">
              <iframe src="https://www.facebook.com/plugins/post.php?href=https%3A%2F%2Fwww.facebook.com%2Fpost%2F456"></iframe>
              <script src="https://platform.twitter.com/widgets.js"></script>
            </div>
            <p>Conclusion paragraph</p>
          </div>
        </article>
      `;
      
      const result = processEmbeddedContent(html);
      
      expect(result).toContain('Article Title');
      expect(result).toContain('Introduction paragraph');
      expect(result).toContain('YouTube Videó');
      expect(result).toContain('Middle paragraph');
      expect(result).toContain('Facebook Poszt');
      expect(result).toContain('[Beágyazott közösségi média widget eltávolítva a jobb kompatibilitás érdekében]');
      expect(result).toContain('Conclusion paragraph');
      expect(result).not.toContain('<iframe');
      expect(result).not.toContain('platform.twitter.com');
    });

    it('should handle malformed iframe src URLs gracefully', () => {
      const html = `
        <div>
          <iframe src="not-a-valid-url"></iframe>
          <iframe src=""></iframe>
          <iframe src="javascript:alert('xss')"></iframe>
        </div>
      `;
      
      const result = processEmbeddedContent(html);
      
      // Should handle gracefully without throwing errors
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
    });

    it('should preserve non-iframe HTML attributes and structure', () => {
      const html = `
        <div class="container" id="main">
          <p class="intro" style="color: blue;">Introduction</p>
          <iframe src="https://www.youtube.com/embed/test123"></iframe>
          <p class="outro" data-test="value">Conclusion</p>
        </div>
      `;
      
      const result = processEmbeddedContent(html);
      
      expect(result).not.toContain('class="container"');
      expect(result).toContain('id="main"');
      expect(result).not.toContain('class="intro"');
      expect(result).toContain('style="color: blue;"');
      expect(result).not.toContain('class="outro"');
      expect(result).toContain('data-test="value"');
      expect(result).toContain('YouTube Videó');
      expect(result).not.toContain('<iframe');
    });

    it('should handle Facebook video iframe without valid URL', () => {
      const html = `
        <div>
          <iframe src="https://www.facebook.com/plugins/video.php?height=476&width=267&t=0"></iframe>
        </div>
      `;
      
      const result = processEmbeddedContent(html);
      
      expect(result).toContain('Facebook Video');
      expect(result).toContain('A videó linkje nem elérhető');
      expect(result).not.toContain('<iframe');
    });

    it('should extract correct video ID from various YouTube URL formats', () => {
      const testCases = [
        {
          html: '<iframe src="https://www.youtube.com/embed/abc123def"></iframe>',
          expectedId: 'abc123def'
        },
        {
          html: '<iframe src="https://youtu.be/xyz789uvw"></iframe>',
          expectedId: 'xyz789uvw'
        },
        {
          html: '<iframe src="https://www.youtube.com/watch?v=mno456pqr"></iframe>',
          expectedId: 'mno456pqr'
        }
      ];

      testCases.forEach(testCase => {
        const result = processEmbeddedContent(testCase.html);
        expect(result).toContain(`https://img.youtube.com/vi/${testCase.expectedId}/maxresdefault.jpg`);
        expect(result).toContain(`https://www.youtube.com/watch?v=${testCase.expectedId}`);
      });
    });

    it('should maintain proper styling in replacement elements', () => {
      const html = '<iframe src="https://www.youtube.com/embed/test123"></iframe>';
      const result = processEmbeddedContent(html);
      
      // Check that styling attributes are present (JSDOM may convert colors to rgb format)
      expect(result).toMatch(/border: 1px solid (rgb\(221, 221, 221\)|#ddd)/);
      expect(result).toContain('padding: 15px');
      expect(result).toMatch(/background-color: (rgb\(248, 249, 250\)|#f8f9fa)/);
      expect(result).toContain('border-radius: 8px');
      expect(result).toContain('display: flex');
      expect(result).toContain('align-items: center');
    });

    it('should handle content with mixed embedded elements', () => {
      const html = `
        <div>
          <h2>Mixed Content Test</h2>
          <iframe src="https://www.youtube.com/embed/video1"></iframe>
          <script src="https://platform.twitter.com/widgets.js"></script>
          <p>Some text between</p>
          <iframe src="https://www.facebook.com/plugins/video.php?href=https%3A%2F%2Fwww.facebook.com%2Fvideo%2F123"></iframe>
          <object data="flash-content.swf"></object>
          <p>Final paragraph</p>
        </div>
      `;
      
      const result = processEmbeddedContent(html);
      
      expect(result).toContain('Mixed Content Test');
      expect(result).toContain('YouTube Videó');
      expect(result).toContain('[Beágyazott közösségi média widget eltávolítva a jobb kompatibilitás érdekében]');
      expect(result).toContain('Some text between');
      expect(result).toContain('Facebook Video');
      expect(result).toContain('[Beágyazott multimédiás tartalom nem támogatott a hírcsatornában]');
      expect(result).toContain('Final paragraph');
      expect(result).not.toContain('<iframe');
      expect(result).not.toContain('<script');
      expect(result).not.toContain('<object');
    });
  });
});
