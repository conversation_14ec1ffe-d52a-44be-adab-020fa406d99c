# Contributing to MTI Feed

## Code Hygiene and Quality Standards

This project maintains high standards for code quality and cleanliness to ensure maintainability and ease of contribution.

### Code Cleanliness Rules

#### 1. No TODO/FIXME Comments
- **Rule**: TODO and FIXME comments are not allowed in the main codebase
- **Rationale**: These comments often accumulate and become stale, making it harder to track actual technical debt
- **Enforcement**: 
  - CI pipeline automatically fails if TODO/FIXME comments are found in source files
  - Pre-commit hook warns developers before committing such comments
- **Alternatives**:
  - Create GitHub issues for work that needs to be done
  - Use clear function/variable names instead of explanatory comments
  - If temporary comments are needed during development, remove them before committing

#### 2. No Commented-Out Code
- **Rule**: Commented-out code blocks should not be committed
- **Rationale**: Version control tracks changes; commented code adds noise and confusion
- **Guidelines**:
  - Remove old code rather than commenting it out
  - If code needs to be temporarily disabled, use feature flags or configuration
  - Trust version control to preserve history of removed code

#### 3. Debug Statement Policy
- **Rule**: Remove debug statements (console.log, console.error for debugging) before committing
- **Acceptable exceptions**: 
  - Production logging statements with appropriate log levels
  - Error handling with proper logging frameworks
- **Guidelines**:
  - Use proper logging (via the logger utility) instead of console statements
  - Remove temporary debugging code before creating pull requests

#### 4. Clean Imports and Dependencies
- **Rule**: Only import what is needed; avoid dead imports
- **Guidelines**:
  - Remove unused imports before committing
  - Use specific imports rather than importing entire modules when possible
  - Keep import statements organized and readable

### Automated Checks

#### CI Pipeline Checks
The GitHub Actions workflow includes:
- TODO/FIXME comment detection in source files
- Commit message validation (conventional commits)
- Test execution
- Build verification

#### Pre-commit Hooks
Available in `.githooks/`:
- `commit-msg`: Validates commit messages using commitlint
- `pre-commit`: Checks for TODO/FIXME comments in staged files

To install hooks locally:
```bash
cp .githooks/commit-msg .git/hooks/commit-msg && chmod +x .git/hooks/commit-msg
cp .githooks/pre-commit .git/hooks/pre-commit && chmod +x .git/hooks/pre-commit
```

### Exception Process

If you need to temporarily bypass these rules:
1. **For TODO/FIXME**: Create a GitHub issue instead and reference it in code comments
2. **For urgent fixes**: Use `git commit --no-verify` but ensure cleanup happens in next commit
3. **For debugging**: Use proper logging levels that can be configured per environment

### Review Guidelines

When reviewing pull requests:
- Check for adherence to these code hygiene standards
- Verify that removed code isn't breaking existing functionality
- Ensure proper test coverage for any changes
- Look for opportunities to further clean up surrounding code

### Tools and Utilities

The project includes several utilities to maintain code quality:
- **ESLint**: For code style and basic quality checks (if configured)
- **Vitest**: For testing with coverage reports
- **Commitlint**: For conventional commit message validation
- **Standard-version**: For automated changelog and version management

## Development Workflow

1. Create feature branch from `main`
2. Make changes following code hygiene rules
3. Run tests locally: `npm test`
4. Check for TODO/FIXME: `grep -r "TODO\|FIXME" src/`
5. Commit with conventional commit format
6. Push and create pull request
7. Ensure CI checks pass
8. Address review feedback
9. Merge after approval

## Questions or Suggestions

If you have questions about these guidelines or suggestions for improvement, please create an issue for discussion.