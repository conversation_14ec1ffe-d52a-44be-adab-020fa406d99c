{"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "docs", "section": "Documentation"}, {"type": "style", "section": "Styles"}, {"type": "refactor", "section": "Code Refactoring"}, {"type": "perf", "section": "Performance Improvements"}, {"type": "test", "section": "Tests"}, {"type": "build", "section": "Build System"}, {"type": "ci", "section": "Continuous Integration"}, {"type": "chore", "hidden": true}, {"type": "revert", "section": "Reverts"}], "commitUrlFormat": "https://github.com/p3g2or/mti-feed/commit/{{hash}}", "compareUrlFormat": "https://github.com/p3g2or/mti-feed/compare/{{previousTag}}...{{currentTag}}", "issueUrlFormat": "https://github.com/p3g2or/mti-feed/issues/{{id}}", "userUrlFormat": "https://github.com/{{user}}", "releaseCommitMessageFormat": "chore(release): {{currentTag}}", "issuePrefixes": ["#"], "header": "# Changelog\n\nAll notable changes to this project will be documented in this file.\n\nThe format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),\nand this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).\n\n"}