{"name": "mti-feed", "version": "1.2.2", "description": "MTI.hu hírek JSON Feed API - automatikus hírlekérés és JSON Feed szolgáltatás", "main": "src/app.js", "type": "module", "scripts": {"start": "node src/app.js", "dev": "node --watch src/app.js", "test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "postinstall": "playwright install", "release": "standard-version", "release:minor": "standard-version --release-as minor", "release:major": "standard-version --release-as major", "release:patch": "standard-version --release-as patch", "release:dry-run": "standard-version --dry-run", "commitlint": "commitlint --edit", "commitlint:test": "echo 'feat: test new feature' | commitlint", "version:check": "standard-version --dry-run"}, "keywords": ["mti", "news", "scraping", "json-feed", "fastify", "playwright"], "author": "p3g2or", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/p3g2or/mti-feed.git"}, "bugs": {"url": "https://github.com/p3g2or/mti-feed/issues"}, "homepage": "https://github.com/p3g2or/mti-feed#readme", "dependencies": {"@fastify/cors": "^11.0.1", "dotenv": "^16.5.0", "fastify": "^5.4.0", "jsdom": "^26.1.0", "node-cron": "^4.1.0", "pino": "^9.3.1", "pino-pretty": "^13.0.0", "playwright": "^1.53.0"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@vitest/coverage-v8": "^3.2.3", "@vitest/ui": "^3.2.1", "standard-version": "^9.5.0", "vitest": "^3.2.4"}, "engines": {"node": ">=18.0.0"}}