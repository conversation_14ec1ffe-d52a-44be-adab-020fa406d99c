# GitHub Copilot Repository Custom Instructions

When suggesting or generating code for the mti-feed project, please adhere to the following guidelines:

## Project Context
This application automatically collects news articles from MTI.hu website and serves them in JSON Feed format.

### Key Features
- Secure MTI.hu login and session management
- Multi-category news scraping with randomized delays
- Standards-compliant JSON Feed 1.1 output
- Embedded content extraction (YouTube, Facebook, Twitter, Instagram)
- Resource-efficient, human-like scraping with session management and rate limiting
- Single-threaded sequential processing with caching for performance

### Technology Stack
- **Runtime**: Node.js
- **Web Framework**: Fastify
- **Web Scraping**: Playwright
- **Scheduling**: node-cron
- **Logging**: Pino
- **Testing**: Vitest
- **Containerization**: Docker (Alpine Linux base)

## Code Organization

### Project Structure
- Maintain clean separation of concerns: `controllers/`, `routes/`, `services/`, `utils/`
- Place handlers in `controllers/`, routes in `routes/`, business logic in `services/`, helpers in `utils/`
- Follow camelCase naming for functions and variables

### Architecture Guidelines
- Use controllers for request handling
- Implement business logic in services
- Place utility functions in utils modules
- Keep routes simple and delegate to controllers

## Code Quality Standards

### Coding Style
- Follow ESLint/Prettier configuration
- Use semicolons and single quotes
- Apply two-space indentation
- Write concise JSDoc comments for exported functions and classes
- Keep functions small and focused
- Apply DRY (Don't Repeat Yourself) principles

### Error Handling & Logging
- Use `utils/errors.js` for standardized error handling
- Log via `utils/logger.js` (Pino) with context fields (`category`, `itemId`, `url`)
- Never expose sensitive data (credentials, tokens) in logs or error messages

## Configuration & Environment

### Configuration Management
- Load defaults from `config/default.json`
- Override settings via `.env` file
- Validate environment variables with `utils/env.js` at startup

### Scraping Implementation
- Implement scraper logic in `services/scraper.js` using Playwright
- Honor randomized delays based on `config.scraper.delay`
- Cache scraped pages in `data/feedCache.json`
- Maintain session state for authenticated requests

## Testing Strategy

### Test Organization
- Unit tests in `tests/unit/`
- Integration tests in `tests/integration/`
- Use Vitest as the testing framework
- Follow existing test naming patterns (`<module>.test.js`)

### Testing Best Practices
- Mock external HTTP calls in unit tests
- Limit real network requests in integration tests
- Maintain test isolation and repeatability
- All tests must pass successfully before creating pull requests (`npm run test:run`)

## API Development

### Endpoint Design
- Define Fastify routes in `routes/` directory
- Register routes in `app.js`
- Validate inputs and return proper HTTP status codes
- Follow RESTful conventions where applicable

## Development Practices

### Documentation & Commits
- Use Conventional Commits format for commit messages
- Update README.md when adding new features or breaking changes
- Keep documentation synchronized with code changes

### Maintenance Guidelines
- Keep this `copilot-instructions.md` file updated when project structure, conventions, or technologies change
- Suggest improvements to coding guidelines based on emerging patterns or issues in the codebase
- Ensure instructions remain relevant to the current project state and development practices

## What to Avoid
- Introducing new directories or altering existing project structure
- Using tabs for indentation (use spaces only)
- Including personal or sensitive credentials in code or logs
- Exposing internal implementation details in API responses
- Do not do anything that is not your task or not related to achieving it
