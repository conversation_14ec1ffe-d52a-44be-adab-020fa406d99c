name: Release

on:
  push:
    branches: [ "main" ]
    paths:
      - 'src/**'
      - 'package.json'
      - 'CHANGELOG.md'
  workflow_dispatch:
    inputs:
      release_type:
        description: 'Release type'
        required: true
        default: 'patch'
        type: choice
        options:
          - patch
          - minor
          - major

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [24.x]
    
    steps:
    - uses: actions/checkout@v4
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    - run: npm ci
    - run: npm run test:run

  release:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch' || (github.event_name == 'push' && contains(github.event.head_commit.message, 'feat:') || contains(github.event.head_commit.message, 'fix:') || contains(github.event.head_commit.message, 'BREAKING CHANGE'))
    
    permissions:
      contents: write
      issues: write
      pull-requests: write
    
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
        token: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Use Node.js 24.x
      uses: actions/setup-node@v4
      with:
        node-version: 24.x
        cache: 'npm'
    
    - run: npm ci
    
    - name: Configure Git
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
    
    - name: Create Release (Auto)
      if: github.event_name == 'push'
      run: |
        npm run release
        git push --follow-tags origin main
    
    - name: Create Release (Manual)
      if: github.event_name == 'workflow_dispatch'
      run: |
        npm run release:${{ github.event.inputs.release_type }}
        git push --follow-tags origin main
    
    - name: Get version
      id: version
      run: echo "version=$(node -p "require('./package.json').version")" >> $GITHUB_OUTPUT
    
    - name: Create GitHub Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: v${{ steps.version.outputs.version }}
        release_name: Release v${{ steps.version.outputs.version }}
        body: |
          See [CHANGELOG.md](https://github.com/p3g2or/mti-feed/blob/main/CHANGELOG.md) for details.
        draft: false
        prerelease: false