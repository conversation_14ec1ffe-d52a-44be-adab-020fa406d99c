# Use an official Node.js runtime as a parent image
FROM node:18-slim

# Set the working directory in the container
WORKDIR /usr/src/app

# Copy package.json and package-lock.json to the working directory
COPY package*.json ./

# Install Playwright browsers
RUN npx playwright install --with-deps

# Install app dependencies
RUN npm ci --only=production

# Copy the rest of the application code to the working directory
COPY . .

# Create data directory and set permissions
RUN mkdir -p /usr/src/app/data && chmod 755 /usr/src/app/data

# Expose the port the app runs on
EXPOSE 3000

# Define the command to run the application
CMD [ "node", "src/app.js" ]
