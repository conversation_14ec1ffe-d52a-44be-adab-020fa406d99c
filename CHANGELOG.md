# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.2.2] - 2025-07-04

### Added
- **Category-based Cache Architecture** - Implemented cache.js with separate in-memory caches per news category
- **Enhanced Error Handling** - Added comprehensive error management with detailed debug information
- **Expanded Test Coverage** - Increased test suite to 148 tests across 13 test files

### Changed
- **Cache Strategy** - Migrated from persistent file-based caching to in-memory category-specific caches
- **Documentation Updates** - Updated all documentation to reflect current architecture and removed outdated references
- **Performance Improvements** - Optimized cache management and error handling

### Removed
- **Legacy Cache Files** - Removed dependency on feedCache.json persistent storage
- **Outdated Documentation** - Cleaned up references to removed tracing system and outdated cache strategy

### Technical Notes
- Cache is now initialized empty on application start with per-category size limits
- Independent category management allows for better scalability and maintenance
- All existing API endpoints remain unchanged, ensuring backward compatibility

## [1.2.1] - 2025-06-17

### Removed
- **BREAKING**: Completely removed tracing system from the application
  - Deleted `src/utils/tracing.js` file
  - Removed all tracing-related tests (`tests/unit/tracing.test.js`, `tests/unit/tracing-toggle.test.js`, `tests/integration/tracing.test.js`)
  - Removed `logWithTrace` and `createTracedLogger` functions from logger utility
  - Removed tracing usage from auth service, scraper service, and app.js
  - Removed `ENABLE_TRACING` environment variable from .env and .env.example
  - Simplified logging to use regular pino logger throughout the application

### Changed
- Updated auth service to use direct logger calls instead of traced logging
- Updated scraper service to use direct logger calls instead of traced logging  
- Updated app.js to use direct logger calls instead of traced logging
- Simplified method signatures by removing traceId parameters
- Updated auth.test.js to remove tracing mocks

### Technical Notes
- This is a breaking change for any external integrations that relied on tracing functionality
- The application now has a simpler, more straightforward logging approach
- All existing functionality remains the same, just without the tracing overhead

## [1.2.0] - 2025-06-17

### Added
- Comprehensive error handling system with custom error types
- Detailed error logging with structured context information  
- Debug information collection for troubleshooting scraping issues
- Session validation and re-authentication logic
- Enhanced authentication flow with better error recovery
- Improved browser and page lifecycle management
- Robust content extraction with embedded content processing
- Advanced article sorting and deduplication
- Comprehensive test coverage for all major components
- Performance monitoring and metrics collection
- Graceful shutdown handling with cleanup procedures
- Human-like behavior simulation with random delays
- Cache size management and persistence
- Environment-based configuration system
- Health check endpoints for monitoring
- JSON Feed 1.1 specification compliance

### Enhanced
- MTI.hu scraping reliability with retry logic
- Date parsing for Hungarian and English formats  
- Content cleaning and HTML processing
- Error logging with contextual debug information
- Browser initialization and session management
- Authentication state tracking and validation
- Article preview extraction and enrichment
- Cache loading and saving with validation
- Configuration management with environment overrides
- Logging system with structured output
- Test infrastructure with comprehensive mocking

### Technical Improvements
- Modular service architecture with dependency injection
- Comprehensive error boundary implementation
- Advanced Playwright browser automation
- Robust HTML parsing and content extraction
- Efficient caching with JSON file persistence
- Environment variable configuration system
- Structured logging with pino integration
- Comprehensive unit and integration testing
- Performance metrics and monitoring
- Graceful error recovery and fallback mechanisms

## [1.1.0] - 2025-06-16

### Added
- Basic MTI.hu article scraping functionality
- Simple authentication system
- JSON feed generation
- Basic caching mechanism

## [1.0.0] - 2025-06-15

### Added
- Initial project setup
- Basic Fastify server
- Project structure and configuration
