// Load environment variables first
import '../utils/env.js';

import { chromium } from 'playwright';
import config from '../utils/config.js';
import { AuthenticationError, ConfigurationError, NetworkError, SessionError } from '../utils/errors.js';
import { randomDelay } from '../utils/helpers.js';
import logger from '../utils/logger.js';

class AuthService {
  constructor(customConfig = {}) {
    this.browser = null;
    this.page = null;
    this.isAuthenticated = false;
    this.sessionCookies = null;
    this.lastLoginTime = null;
    
    // Merge customConfig with default config
    this.config = { ...config, ...customConfig };
    
    // Ensure timeouts exist with defensive fallbacks for incomplete configs
    if (!this.config.mti?.timeouts) {
      this.config.mti = {
        ...this.config.mti,
        timeouts: {
          pageLoad: 60000,
          networkIdle: 60000,
          selector: 60000,
          formSubmit: 60000,
          authCheck: 60000
        }
      };
    } else {
      // Ensure all individual timeout properties exist
      const defaults = {
        pageLoad: 60000,
        networkIdle: 60000,
        selector: 60000,
        formSubmit: 60000,
        authCheck: 60000
      };
      this.config.mti.timeouts = { ...defaults, ...this.config.mti.timeouts };
    }
  }

  /**
   * Checks if the browser has been initialized.
   * @returns {boolean} True if the browser is initialized, false otherwise.
   */
  isBrowserInitialized() {
    return !!this.browser;
  }

  /**
   * Initialize browser instance
   */
  async initBrowser() {
    if (this.browser) {
      logger.debug('Browser already initialized, reusing existing instance');
      return this.browser;
    }

    try {
      logger.info('Initializing browser...');
      
      this.browser = await chromium.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-dev-shm-usage',
          '--disable-blink-features=AutomationControlled'
        ]
      });

      const context = await this.browser.newContext({
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      });

      this.page = await context.newPage();
      
      // Set random viewport to appear more human-like
      await this.page.setViewportSize({
        width: 1366 + Math.floor(Math.random() * 200),
        height: 768 + Math.floor(Math.random() * 200)
      });

      logger.info('Browser initialized successfully');
      return this.browser;
    } catch (error) {
      logger.error('Failed to initialize browser', {
        error: error.message
      });
      throw new NetworkError(`Browser initialization failed: ${error.message}`, null, 'browser');
    }
  }

  /**
   * Login to MTI.hu
   * @param {string} email - Login email
   * @param {string} password - Login password 
   */
  async login(email, password) {
    try {
      await this.initBrowser();
      
      logger.info('Starting login process...');
      
      // Navigate to login page with fallback timeout
      await this.page.goto(this.config.mti.loginUrl, { 
        waitUntil: 'networkidle',
        timeout: this.config.mti.timeouts.pageLoad
      });
      logger.debug('Navigated to login page', {
        url: this.config.mti.loginUrl
      });
      
      // Human-like delay
      await randomDelay(2000, 4000);
      
      // Look for "Registered visitor" button
      logger.info('Looking for registration button...');
      const regButton = await this.page.locator('text=Regisztrált látogatóként').first();
      
      if (await regButton.isVisible()) {
        await regButton.click();
        logger.info('Clicked registration button');
        await this.page.waitForLoadState('networkidle', { 
          timeout: this.config.mti.timeouts.networkIdle
        }); 
      }

      // Wait for login form with fallback timeout
      await this.page.waitForSelector('input[type="email"], input[name="email"]', { 
        timeout: this.config.mti.timeouts.selector
      });
      
      // Fill email field
      logger.info('Filling email field...');
      const emailField = await this.page.locator('input[type="email"], input[name="email"]').first();
      await emailField.fill(email);
      await this.page.waitForLoadState('networkidle', { 
        timeout: this.config.mti.timeouts.networkIdle
      }); 
      await randomDelay(500, 1000);

      // Fill password field
      logger.info('Filling password field...');
      const passwordField = await this.page.locator('input[type="password"], input[name="password"]').first();
      await passwordField.fill(password);
      await randomDelay(500, 1000);

      // Submit form
      logger.info('Submitting login form...');
      const submitButton = await this.page.locator('button[type="submit"], input[type="submit"]').first();
      await submitButton.click();

      // Wait for navigation or success indicator with fallback timeout
      await this.page.waitForLoadState('networkidle', { 
        timeout: this.config.mti.timeouts.formSubmit
      });
      
      // Check if login was successful
      const currentUrl = this.page.url();
      const cookies = await this.page.context().cookies();
      
      // Look for session cookies or successful login indicators
      const hasSessionCookie = cookies.some(cookie => 
        cookie.name.toLowerCase().includes('session') || 
        cookie.name.toLowerCase().includes('auth') ||
        cookie.name.toLowerCase().includes('login')
      );

      if (hasSessionCookie || !currentUrl.includes('bejelentkezes')) {
        this.isAuthenticated = true;
        this.sessionCookies = cookies;
        this.lastLoginTime = new Date();
        logger.info('Login successful', {
          hasSessionCookie,
          currentUrl,
          cookieCount: cookies.length
        });
        return true;
      } else {
        throw new AuthenticationError('Login failed - no session cookie found', { 
          url: currentUrl, 
          cookieCount: cookies.length 
        });
      }

    } catch (error) {
      logger.error('Login failed', {
        error: error.message
      });
      this.isAuthenticated = false;
      
      if (error instanceof AuthenticationError) {
        throw error;
      } else if (error.message.includes('timeout') || error.message.includes('network')) {
        throw new NetworkError(`Login network error: ${error.message}`, null, this.config.mti.loginUrl);
      } else {
        throw new AuthenticationError(`Authentication failed: ${error.message}`, { originalError: error.message });
      }
    }
  }

  /**
   * Check if still authenticated
   */
  async checkAuthStatus() {
    if (!this.isAuthenticated || !this.page) {
      return false;
    }

    try {
      // Navigate to a protected page to test authentication with fallback timeout
      await this.page.goto(`${this.config.mti.baseUrl}/kozelet`, { 
        waitUntil: 'networkidle',
        timeout: this.config.mti.timeouts.authCheck
      });

      const currentUrl = this.page.url();
      
      // If redirected to login, session expired
      if (currentUrl.includes('bejelentkezes')) {
        logger.warn('Session expired, need to re-authenticate');
        this.isAuthenticated = false;
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Auth check failed:', error);
      this.isAuthenticated = false;
      return false;
    }
  }

  /**
   * Maintain session - re-login if needed
   */
  async maintainSession() {
    const email = process.env.MTI_EMAIL;
    const password = process.env.MTI_PASSWORD;

    if (!email || !password) {
      throw new ConfigurationError('MTI_EMAIL and MTI_PASSWORD environment variables are required', 'credentials');
    }

    if (!this.isAuthenticated) {
      logger.info('Not authenticated, performing login...');
      return await this.login(email, password);
    }

    // Check if session is still valid (every 10 minutes instead of 30)
    const now = new Date();
    const timeSinceLogin = now - this.lastLoginTime;
    const tenMinutes = 10 * 60 * 1000;

    if (timeSinceLogin > tenMinutes) {
      logger.info('Checking session validity (10 min interval)...');
      const isValid = await this.checkAuthStatus();
      
      if (!isValid) {
        logger.info('Session expired, re-authenticating...');
        return await this.login(email, password);
      }
    }

    return true;
  }

  /**
   * Get current page for scraping
   */
  getPage() {
    if (!this.page || !this.isAuthenticated) {
      throw new SessionError('Not authenticated or page not available', 'getPage');
    }
    return this.page;
  }

  /**
   * Get session cookies
   */
  getSessionCookies() {
    return this.sessionCookies;
  }

  /**
   * Cleanup browser resources
   */
  async cleanup() {
    try {
      if (this.page) {
        await this.page.close();
        this.page = null;
      }
      if (this.browser) {
        await this.browser.close();
        this.browser = null;
      }
      this.isAuthenticated = false;
      this.sessionCookies = null;
      logger.info('Browser cleanup completed');
    } catch (error) {
      logger.error('Error during cleanup:', error);
    }
  }
}

export default AuthService;
