import { getMetrics } from '../controllers/metrics.js';

export default async function metricsRoutes(fastify, options) {
  fastify.get('/metrics', {
    schema: {
      description: 'Get application metrics and statistics',
      tags: ['monitoring'],
      response: {
        200: {
          type: 'object',
          properties: {
            status: { type: 'string' },
            timestamp: { type: 'string' },
            uptime: {
              type: 'object',
              properties: {
                seconds: { type: 'number' },
                human: { type: 'string' }
              }
            },
            memory: {
              type: 'object',
              properties: {
                process: {
                  type: 'object',
                  properties: {
                    rss: { type: 'number' },
                    heapTotal: { type: 'number' },
                    heapUsed: { type: 'number' },
                    external: { type: 'number' }
                  }
                },
                cache: {
                  type: 'object',
                  properties: {
                    totalMB: { type: 'number' },
                    totalBytes: { type: 'number' },
                    categories: { type: 'object' }
                  }
                }
              }
            },
            cache: {
              type: 'object',
              properties: {
                type: { type: 'string' },
                totalArticles: { type: 'number' },
                totalCategories: { type: 'number' },
                availableCategories: { type: 'array', items: { type: 'string' } },
                timeSinceLastUpdate: {
                  type: ['object', 'null'],
                  properties: {
                    seconds: { type: 'number' },
                    human: { type: 'string' }
                  }
                },
                categories: { type: 'object' },
                limits: { type: 'object' }
              }
            },
            statistics: {
              type: 'object',
              properties: {
                recentArticles: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      title: { type: 'string' },
                      date_published: { type: 'string' },
                      tags: { type: 'array', items: { type: 'string' } }
                    }
                  }
                },
                categoryBreakdown: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      category: { type: 'string' },
                      count: { type: 'number' },
                      utilizationPercent: { type: 'number' },
                      lastUpdated: { type: ['string', 'null'] }
                    }
                  }
                }
              }
            },
            version: { type: 'string' }
          }
        }
      }
    }
  }, getMetrics);
}
