import { config as dotenvConfig } from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Get the directory of the current module
const __dirname = dirname(fileURLToPath(import.meta.url));
const rootDir = join(__dirname, '../..');

// Load environment variables from .env file in the project root
const result = dotenvConfig({ path: join(rootDir, '.env') });

if (result.error) {
  console.warn('Warning: Could not load .env file:', result.error.message);
}

export default {};
