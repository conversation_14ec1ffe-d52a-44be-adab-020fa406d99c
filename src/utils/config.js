// Load environment variables first
import './env.js';

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { readFileSync } from 'fs';

const __dirname = dirname(fileURLToPath(import.meta.url));
const configPath = join(__dirname, '../../config/default.json');

export const config = JSON.parse(readFileSync(configPath, 'utf8'));

// Environment variable overrides

// Server configuration
if (process.env.PORT) {
  config.server.port = parseInt(process.env.PORT);
}

if (process.env.HOST) {
  config.server.host = process.env.HOST;
}

if (process.env.TIMEZONE) {
  config.server.timezone = process.env.TIMEZONE;
}

// MTI configuration
if (process.env.MTI_EMAIL) {
  config.mti.email = process.env.MTI_EMAIL;
}

if (process.env.MTI_PASSWORD) {
  config.mti.password = process.env.MTI_PASSWORD;
}

if (process.env.MTI_BASE_URL) {
  config.mti.baseUrl = process.env.MTI_BASE_URL;
}

if (process.env.MTI_LOGIN_URL) {
  config.mti.loginUrl = process.env.MTI_LOGIN_URL;
}

// MTI timeout overrides
if (process.env.MTI_TIMEOUT_PAGE_LOAD) {
  config.mti.timeouts.pageLoad = parseInt(process.env.MTI_TIMEOUT_PAGE_LOAD);
}

if (process.env.MTI_TIMEOUT_NETWORK_IDLE) {
  config.mti.timeouts.networkIdle = parseInt(process.env.MTI_TIMEOUT_NETWORK_IDLE);
}

if (process.env.MTI_TIMEOUT_SELECTOR) {
  config.mti.timeouts.selector = parseInt(process.env.MTI_TIMEOUT_SELECTOR);
}

if (process.env.MTI_TIMEOUT_FORM_SUBMIT) {
  config.mti.timeouts.formSubmit = parseInt(process.env.MTI_TIMEOUT_FORM_SUBMIT);
}

if (process.env.MTI_TIMEOUT_AUTH_CHECK) {
  config.mti.timeouts.authCheck = parseInt(process.env.MTI_TIMEOUT_AUTH_CHECK);
}

// Scraping configuration
if (process.env.SCRAPE_INTERVAL) {
  config.scraping.interval = process.env.SCRAPE_INTERVAL;
}

if (process.env.SCRAPE_TIMEZONE) {
  config.scraping.timezone = process.env.SCRAPE_TIMEZONE;
}

if (process.env.SCRAPE_TIMEOUT) {
  config.scraping.timeout = parseInt(process.env.SCRAPE_TIMEOUT);
}

if (process.env.SCRAPE_RETRIES) {
  config.scraping.retries = parseInt(process.env.SCRAPE_RETRIES);
}

if (process.env.SCRAPE_HUMAN_DELAY_MIN) {
  config.scraping.humanDelay.min = parseInt(process.env.SCRAPE_HUMAN_DELAY_MIN);
}

if (process.env.SCRAPE_HUMAN_DELAY_MAX) {
  config.scraping.humanDelay.max = parseInt(process.env.SCRAPE_HUMAN_DELAY_MAX);
}

if (process.env.SCRAPE_JITTER_MAX) {
  config.scraping.jitterSeconds.max = parseInt(process.env.SCRAPE_JITTER_MAX);
}

if (process.env.SCRAPE_CONTENT_READY_TIMEOUT) {
  config.scraping.contentReadyTimeout = parseInt(process.env.SCRAPE_CONTENT_READY_TIMEOUT);
}

if (process.env.MAX_NEW_ARTICLES_PER_RUN) {
  config.scraping.maxNewArticlesPerRun = parseInt(process.env.MAX_NEW_ARTICLES_PER_RUN);
}

if (process.env.CACHE_MAX_SIZE) {
  config.scraping.cacheMaxSize = parseInt(process.env.CACHE_MAX_SIZE);
}

// Category-specific cache limits (optional environment variables)
if (process.env.CACHE_LIMIT_KOZELET) {
  config.scraping.categoryLimits.kozelet = parseInt(process.env.CACHE_LIMIT_KOZELET);
}
if (process.env.CACHE_LIMIT_GAZDASAG) {
  config.scraping.categoryLimits.gazdasag = parseInt(process.env.CACHE_LIMIT_GAZDASAG);
}
if (process.env.CACHE_LIMIT_VILAG) {
  config.scraping.categoryLimits.vilag = parseInt(process.env.CACHE_LIMIT_VILAG);
}
if (process.env.CACHE_LIMIT_KULTURA) {
  config.scraping.categoryLimits.kultura = parseInt(process.env.CACHE_LIMIT_KULTURA);
}
if (process.env.CACHE_LIMIT_SPORT) {
  config.scraping.categoryLimits.sport = parseInt(process.env.CACHE_LIMIT_SPORT);
}
if (process.env.CACHE_LIMIT_ENGLISH) {
  config.scraping.categoryLimits.english = parseInt(process.env.CACHE_LIMIT_ENGLISH);
}

// Feed configuration
if (process.env.FEED_TITLE) {
  config.feed.title = process.env.FEED_TITLE;
}

if (process.env.FEED_HOME_PAGE_URL) {
  config.feed.homePageUrl = process.env.FEED_HOME_PAGE_URL;
}

if (process.env.FEED_ICON) {
  config.feed.icon = process.env.FEED_ICON;
}

// Logging configuration
if (process.env.LOG_LEVEL) {
  config.logging.level = process.env.LOG_LEVEL;
}

export default config;
