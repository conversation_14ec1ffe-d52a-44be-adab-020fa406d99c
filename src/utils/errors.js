/**
 * Custom error classes for MTI Feed API
 */

/**
 * Base error class for MTI Feed application
 */
export class MTIError extends Error {
  constructor(message, code = 'MTI_ERROR') {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    this.timestamp = new Date().toISOString();
    
    // Maintains proper stack trace for where our error was thrown
    Error.captureStackTrace(this, this.constructor);
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      timestamp: this.timestamp
    };
  }
}

/**
 * Authentication related errors
 */
export class AuthenticationError extends MTIError {
  constructor(message, details = null) {
    super(message, 'AUTH_ERROR');
    this.details = details;
  }
}

/**
 * Network related errors
 */
export class NetworkError extends MTIError {
  constructor(message, statusCode = null, url = null) {
    super(message, 'NETWORK_ERROR');
    this.statusCode = statusCode;
    this.url = url;
  }
}

/**
 * Web scraping related errors
 */
export class ScrapingError extends MTIError {
  constructor(message, url = null, selector = null) {
    super(message, 'SCRAPING_ERROR');
    this.url = url;
    this.selector = selector;
  }
}

/**
 * HTML parsing related errors
 */
export class ParsingError extends MTIError {
  constructor(message, html = null, field = null) {
    super(message, 'PARSING_ERROR');
    this.html = html ? html.substring(0, 200) + '...' : null; // Truncate for logging
    this.field = field;
  }
}

/**
 * Configuration related errors
 */
export class ConfigurationError extends MTIError {
  constructor(message, configKey = null) {
    super(message, 'CONFIG_ERROR');
    this.configKey = configKey;
  }
}

/**
 * Session management errors
 */
export class SessionError extends MTIError {
  constructor(message, action = null) {
    super(message, 'SESSION_ERROR');
    this.action = action;
  }
}

/**
 * Validation errors
 */
export class ValidationError extends MTIError {
  constructor(message, field = null, value = null) {
    super(message, 'VALIDATION_ERROR');
    this.field = field;
    this.value = value;
  }
}

export default {
  MTIError,
  AuthenticationError,
  NetworkError,
  ScrapingError,
  ParsingError,
  ConfigurationError,
  SessionError,
  ValidationError
};
