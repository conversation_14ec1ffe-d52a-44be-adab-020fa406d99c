/**
 * Standardized error handling utilities for MTI Feed API
 */

import { MTIError } from './errors.js';

/**
 * Wraps a controller function with standardized error handling
 * @param {Function} handler - The controller function to wrap
 * @returns {Function} - Wrapped controller function
 */
export function withErrorHandling(handler) {
  return async (request, reply) => {
    try {
      const result = await handler(request, reply);
      return result;
    } catch (error) {
      // Log the error with context
      const logger = request.server.log;
      const errorContext = {
        method: request.method,
        url: request.url,
        userAgent: request.headers['user-agent'],
        ip: request.ip,
        errorName: error.name,
        errorCode: error.code || 'UNKNOWN_ERROR'
      };

      if (error instanceof MTIError) {
        // Custom MTI errors - log with structured data
        logger.error({ 
          err: error,
          ...errorContext,
          errorDetails: error.toJSON()
        }, `Request failed: ${error.message}`);
        
        // Return appropriate HTTP status based on error type
        const statusCode = getHttpStatusForError(error);
        return reply.status(statusCode).send({
          error: 'Request failed',
          message: error.message,
          code: error.code,
          timestamp: error.timestamp
        });
      } else {
        // Unexpected/system errors - log with full details
        logger.error({ 
          err: error,
          ...errorContext
        }, `Unexpected error in request: ${error.message}`);
        
        // Don't expose internal error details to clients
        return reply.status(500).send({
          error: 'Internal server error',
          message: 'An unexpected error occurred',
          code: 'INTERNAL_ERROR',
          timestamp: new Date().toISOString()
        });
      }
    }
  };
}

/**
 * Maps MTI error types to appropriate HTTP status codes
 * @param {MTIError} error - The MTI error instance
 * @returns {number} - HTTP status code
 */
function getHttpStatusForError(error) {
  const errorTypeMap = {
    'AuthenticationError': 401,
    'NetworkError': 502,
    'ScrapingError': 503,
    'ParsingError': 422,
    'ConfigurationError': 500,
    'SessionError': 401,
    'ValidationError': 400,
    'MTIError': 500
  };
  
  return errorTypeMap[error.name] || 500;
}

/**
 * Global error handler for Fastify
 * @param {Error} error - The error that occurred
 * @param {Object} request - Fastify request object
 * @param {Object} reply - Fastify reply object
 */
export function globalErrorHandler(error, request, reply) {
  const logger = request.server.log;
  const errorContext = {
    method: request.method,
    url: request.url,
    userAgent: request.headers['user-agent'],
    ip: request.ip,
    errorName: error.name
  };

  if (error instanceof MTIError) {
    logger.error({ 
      err: error,
      ...errorContext,
      errorDetails: error.toJSON()
    }, `Global error handler: ${error.message}`);
    
    const statusCode = getHttpStatusForError(error);
    reply.status(statusCode).send({
      error: 'Request failed',
      message: error.message,
      code: error.code,
      timestamp: error.timestamp
    });
  } else {
    logger.error({ 
      err: error,
      ...errorContext
    }, `Global error handler: Unexpected error - ${error.message}`);
    
    reply.status(500).send({
      error: 'Internal server error',
      message: 'An unexpected error occurred',
      code: 'INTERNAL_ERROR',
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Wraps an async function with error handling for non-HTTP contexts
 * Logs errors and optionally exits the process
 * @param {Function} asyncFn - The async function to wrap
 * @param {Object} options - Configuration options
 * @param {boolean} options.exitOnError - Whether to exit process on error (default: false)
 * @param {Object} options.logger - Logger instance to use
 * @param {string} options.context - Context description for logging
 * @returns {Function} - Wrapped function
 */
export function withAsyncErrorHandling(asyncFn, options = {}) {
  const { exitOnError = false, logger, context = 'async operation' } = options;
  
  return async (...args) => {
    try {
      return await asyncFn(...args);
    } catch (error) {
      const errorData = {
        context,
        errorName: error.name,
        errorCode: error.code || 'UNKNOWN_ERROR'
      };

      if (error instanceof MTIError) {
        const logMessage = `${context} failed: ${error.message}`;
        if (logger) {
          logger.error({ 
            err: error,
            ...errorData,
            errorDetails: error.toJSON()
          }, logMessage);
        } else {
          console.error(logMessage, errorData);
        }
      } else {
        const logMessage = `${context} failed with unexpected error: ${error.message}`;
        if (logger) {
          logger.error({ 
            err: error,
            ...errorData
          }, logMessage);
        } else {
          console.error(logMessage, errorData);
        }
      }

      if (exitOnError) {
        process.exit(1);
      }
      
      throw error; // Re-throw to maintain error propagation
    }
  };
}

export default {
  withErrorHandling,
  globalErrorHandler,
  withAsyncErrorHandling,
  getHttpStatusForError
};

export { getHttpStatusForError };