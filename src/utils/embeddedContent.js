import { JSD<PERSON> } from 'jsdom';

/**
 * Utility functions for processing embedded content in articles
 * Handles iframes, social media embeds, and other embedded elements
 * to make them more compatible with feed readers.
 */

/**
 * Processes embedded content to make it more feed-reader friendly
 * @param {string} htmlContent - The raw HTML content
 * @param {string} [summary] - Optional summary text to remove from content if it matches
 * @returns {string} - Processed HTML content with embedded elements handled
 */
export function processEmbeddedContent(htmlContent, summary) {
  if (!htmlContent || typeof htmlContent !== 'string') {
    return htmlContent;
  }

  // Create a JSDOM instance to safely parse and manipulate the HTML
  const dom = new JSDOM(`<!DOCTYPE html><body>${htmlContent}</body></html>`);
  const { document } = dom.window;
  const container = document.body;

  // Remove all class attributes for cleaner HTML
  container.querySelectorAll('*').forEach(el => {
    if (el.hasAttribute('class')) {
      el.removeAttribute('class');
    }
  });

  // Process iframes
  const iframes = container.querySelectorAll('iframe');
  iframes.forEach(iframe => {
    const replacement = processIframe(iframe, document);
    if (replacement) {
      iframe.parentNode.replaceChild(replacement, iframe);
    }
  });

  // Process other embedded content
  processOtherEmbeds(container, document);

  // Remove ?itok=... parameters from image URLs and link URLs
  container.querySelectorAll('img').forEach(img => {
    if (img.src && img.src.includes('?itok=')) {
      img.src = img.src.split('?itok=')[0];
    }
  });
  
  container.querySelectorAll('a').forEach(link => {
    if (link.href && link.href.includes('?itok=')) {
      link.href = link.href.split('?itok=')[0];
    }
  });

  return container.innerHTML;
}

/**
 * Processes individual iframe elements
 * @param {HTMLIFrameElement} iframe - The iframe element to process
 * @param {Document} document - The DOM document object
 * @returns {HTMLElement|null} - Replacement element or null if no replacement needed
 */
function processIframe(iframe, document) {
  const src = iframe.getAttribute('src');
  if (!src) return null;

  // Handle Facebook video embeds
  if (src.includes('facebook.com/plugins/video.php')) {
    return createFacebookVideoReplacement(src, document);
  }

  // Handle Facebook post embeds
  if (src.includes('facebook.com/plugins/post.php')) {
    return createFacebookPostReplacement(src, document);
  }

  // Handle YouTube embeds
  if (src.includes('youtube.com/embed') || src.includes('youtu.be') || src.includes('youtube.com/watch')) {
    return createYouTubeReplacement(src, document);
  }

  // Handle Twitter embeds
  if (src.includes('platform.twitter.com')) {
    return createTwitterReplacement(document);
  }

  // Handle Instagram embeds
  if (src.includes('instagram.com')) {
    return createInstagramReplacement(document);
  }

  // Generic iframe replacement
  return createGenericIframeReplacement(src, document);
}

/**
 * Creates a replacement for Facebook video iframes
 */
function createFacebookVideoReplacement(src, document) {
  const container = document.createElement('div');
  container.style.cssText = 'border: 1px solid #ddd; padding: 15px; margin: 10px 0; background-color: #f8f9fa; border-radius: 8px;';

  const urlMatch = src.match(/href=([^&]+)/);
  const videoUrl = urlMatch ? decodeURIComponent(urlMatch[1]) : null;

  container.innerHTML = `
    <div style="display: flex; align-items: center; margin-bottom: 10px;">
      <svg width="20" height="20" viewBox="0 0 24 24" fill="#1877f2" style="margin-right: 8px;">
        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
      </svg>
      <strong style="color: #1877f2;">Facebook Video</strong>
    </div>
    <p style="margin: 8px 0; color: #666;">
      Ez a tartalom egy Facebook videó, amely nem jeleníthető meg közvetlenül a hírcsatornában.
    </p>
    ${videoUrl ? `
      <a href="${videoUrl}" 
         target="_blank" 
         style="display: inline-block; background-color: #1877f2; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; font-weight: bold;">
        🎥 Videó megtekintése Facebookon
      </a>
    ` : `
      <p style="margin: 8px 0; font-style: italic; color: #999;">
        A videó linkje nem elérhető.
      </p>
    `}
  `;

  return container;
}

/**
 * Creates a replacement for Facebook post iframes
 */
function createFacebookPostReplacement(src, document) {
  const container = document.createElement('div');
  container.style.cssText = 'border: 1px solid #ddd; padding: 15px; margin: 10px 0; background-color: #f8f9fa; border-radius: 8px;';

  const urlMatch = src.match(/href=([^&]+)/);
  const postUrl = urlMatch ? decodeURIComponent(urlMatch[1]) : null;

  container.innerHTML = `
    <div style="display: flex; align-items: center; margin-bottom: 10px;">
      <svg width="20" height="20" viewBox="0 0 24 24" fill="#1877f2" style="margin-right: 8px;">
        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
      </svg>
      <strong style="color: #1877f2;">Facebook Poszt</strong>
    </div>
    <p style="margin: 8px 0; color: #666;">
      Ez a tartalom egy Facebook poszt, amely nem jeleníthető meg közvetlenül a hírcsatornában.
    </p>
    ${postUrl ? `
      <a href="${postUrl}" 
         target="_blank" 
         style="display: inline-block; background-color: #1877f2; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; font-weight: bold;">
        📱 Poszt megtekintése Facebookon
      </a>
    ` : ''}
  `;

  return container;
}

/**
 * Creates a replacement for YouTube iframes
 */
function createYouTubeReplacement(src, document) {
  const container = document.createElement('div');
  container.style.cssText = 'border: 1px solid #ddd; padding: 15px; margin: 10px 0; background-color: #f8f9fa; border-radius: 8px;';

  let videoId = null;
  const patterns = [
    /(?:youtube\.com\/embed\/)([a-zA-Z0-9_-]+)/,
    /(?:youtu\.be\/)([a-zA-Z0-9_-]+)/,
    /(?:youtube\.com\/watch\?v=)([a-zA-Z0-9_-]+)/
  ];

  for (const pattern of patterns) {
    const match = src.match(pattern);
    if (match) {
      videoId = match[1];
      break;
    }
  }

  const thumbnailUrl = videoId ? `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg` : null;
  const videoUrl = videoId ? `https://www.youtube.com/watch?v=${videoId}` : src;

  container.innerHTML = `
    <div style="display: flex; align-items: center; margin-bottom: 10px;">
      <svg width="20" height="20" viewBox="0 0 24 24" fill="#FF0000" style="margin-right: 8px;">
        <path d="M23.498 6.186a2.996 2.996 0 0 0-2.106-2.125C19.505 3.546 12 3.546 12 3.546s-7.505 0-9.392.515A2.996 2.996 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a2.996 2.996 0 0 0 2.106 2.125c1.887.515 9.392.515 9.392.515s7.505 0 9.392-.515a2.996 2.996 0 0 0 2.106-2.125C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.546 15.568V8.432L15.818 12l-6.272 3.568z"/>
      </svg>
      <strong style="color: #FF0000;">YouTube Videó</strong>
    </div>
    ${thumbnailUrl ? `
      <div style="margin: 10px 0;">
        <img src="${thumbnailUrl}" 
             alt="YouTube videó thumbnail" 
             style="max-width: 100%; height: auto; border-radius: 4px;"
             onerror="this.style.display='none'">
      </div>
    ` : ''}
    <p style="margin: 8px 0; color: #666;">
      Ez a tartalom egy YouTube videó, amely nem jeleníthető meg közvetlenül a hírcsatornában.
    </p>
    <a href="${videoUrl}" 
       target="_blank" 
       style="display: inline-block; background-color: #FF0000; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; font-weight: bold;">
      ▶️ Videó megtekintése YouTube-on
    </a>
  `;

  return container;
}

/**
 * Creates a replacement for Twitter iframes
 */
function createTwitterReplacement(document) {
  const container = document.createElement('div');
  container.style.cssText = 'border: 1px solid #ddd; padding: 15px; margin: 10px 0; background-color: #f8f9fa; border-radius: 8px;';

  container.innerHTML = `
    <div style="display: flex; align-items: center; margin-bottom: 10px;">
      <svg width="20" height="20" viewBox="0 0 24 24" fill="#1DA1F2" style="margin-right: 8px;">
        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
      </svg>
      <strong style="color: #1DA1F2;">Twitter Poszt</strong>
    </div>
    <p style="margin: 8px 0; color: #666;">
      Ez a tartalom egy Twitter poszt, amely nem jeleníthető meg közvetlenül a hírcsatornában.
    </p>
    <a href="https://twitter.com" 
       target="_blank" 
       style="display: inline-block; background-color: #1DA1F2; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; font-weight: bold;">
      🐦 Poszt megtekintése Twitteren
    </a>
  `;

  return container;
}

/**
 * Creates a replacement for Instagram iframes
 */
function createInstagramReplacement(document) {
  const container = document.createElement('div');
  container.style.cssText = 'border: 1px solid #ddd; padding: 15px; margin: 10px 0; background-color: #f8f9fa; border-radius: 8px;';

  container.innerHTML = `
    <div style="display: flex; align-items: center; margin-bottom: 10px;">
      <svg width="20" height="20" viewBox="0 0 24 24" fill="#E4405F" style="margin-right: 8px;">
        <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
      </svg>
      <strong style="color: #E4405F;">Instagram Poszt</strong>
    </div>
    <p style="margin: 8px 0; color: #666;">
      Ez a tartalom egy Instagram poszt, amely nem jeleníthető meg közvetlenül a hírcsatornában.
    </p>
    <a href="https://instagram.com" 
       target="_blank" 
       style="display: inline-block; background-color: #E4405F; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; font-weight: bold;">
      📷 Poszt megtekintése Instagramon
    </a>
  `;

  return container;
}

/**
 * Creates a generic replacement for unrecognized iframes
 */
function createGenericIframeReplacement(src, document) {
  const container = document.createElement('div');
  container.style.cssText = 'border: 1px solid #ddd; padding: 15px; margin: 10px 0; background-color: #f8f9fa; border-radius: 8px;';

  let domain = '';
  try {
    const url = new URL(src);
    domain = url.hostname;
  } catch (e) {
    domain = 'külső forrás';
  }

  container.innerHTML = `
    <div style="display: flex; align-items: center; margin-bottom: 10px;">
      <svg width="20" height="20" viewBox="0 0 24 24" fill="#666" style="margin-right: 8px;">
        <path d="M14,3V5H17.59L7.76,14.83L9.17,16.24L19,6.41V10H21V3M19,19H5V5H12V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V12H19V19Z"/>
      </svg>
      <strong style="color: #666;">Beágyazott tartalom</strong>
    </div>
    <p style="margin: 8px 0; color: #666;">
      Ez a tartalom egy beágyazott elem (${domain}), amely nem jeleníthető meg közvetlenül a hírcsatornában.
    </p>
    <a href="${src}" 
       target="_blank" 
       style="display: inline-block; background-color: #666; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; font-weight: bold;">
      🔗 Tartalom megtekintése külön ablakban
    </a>
  `;

  return container;
}

/**
 * Processes other types of embedded content beyond iframes
 */
function processOtherEmbeds(containerElement, document) {
  // Handle embedded script tags (social media widgets)
  const scriptTags = containerElement.querySelectorAll('script[src*="platform.twitter.com"], script[src*="instagram.com"], script[src*="facebook.com"], script[src*="connect.facebook.net"]');
  scriptTags.forEach(script => {
    const replacement = document.createElement('div');
    replacement.style.cssText = 'border: 1px solid #ddd; padding: 15px; margin: 10px 0; background-color: #f8f9fa; border-radius: 8px;';
    replacement.innerHTML = `
      <p style="margin: 0; color: #666; font-style: italic;">
        [Beágyazott közösségi média widget eltávolítva a jobb kompatibilitás érdekében]
      </p>
    `;
    script.parentNode.replaceChild(replacement, script);
  });

  // Handle object and embed tags
  const objectTags = containerElement.querySelectorAll('object, embed');
  objectTags.forEach(obj => {
    const replacement = document.createElement('div');
    replacement.style.cssText = 'border: 1px solid #ddd; padding: 15px; margin: 10px 0; background-color: #f8f9fa; border-radius: 8px;';
    replacement.innerHTML = `
      <p style="margin: 0; color: #666; font-style: italic;">
        [Beágyazott multimédiás tartalom nem támogatott a hírcsatornában]
      </p>
    `;
    obj.parentNode.replaceChild(replacement, obj);
  });
}
