import { withErrorHandling } from '../utils/errorHandler.js';

const getHealthStatusHandler = async (request, reply) => {
  // Basic health check - could be extended to check database connections, etc.
  const healthData = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0' // Consider making version dynamic from package.json
  };

  // Optional: Add more health checks here (database, external services, etc.)
  const cachedFeedData = request.server.cachedFeedData;
  if (cachedFeedData) {
    healthData.cache = {
      hasData: cachedFeedData.items && cachedFeedData.items.length > 0,
      lastUpdated: cachedFeedData.lastUpdated,
      articlesCount: cachedFeedData.items ? cachedFeedData.items.length : 0
    };
  }

  return healthData;
};

export const getHealthStatus = withErrorHandling(getHealthStatusHandler);
