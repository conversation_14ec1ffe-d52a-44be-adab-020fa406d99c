// This controller uses the CacheManager for category-specific feeds,
// which is provided by app.js when registering the route.

import { withErrorHandling } from '../utils/errorHandler.js';
import { ValidationError } from '../utils/errors.js';

const getFeedHandler = async (request, reply) => {
  // Accessing cacheManager decorated on the fastify instance
  const cacheManager = request.server.cacheManager;
  const { category: requestedCategory } = request.params; // Extract category from request.params object

  if (!cacheManager) {
    // This case should ideally not happen if app.js correctly provides it
    return reply.status(500).send({ error: "Cache manager not configured." });
  }

  if (requestedCategory !== undefined) {
    // Validate category parameter
    if (typeof requestedCategory !== 'string' || requestedCategory.trim() === '') {
      throw new ValidationError('Invalid category parameter: category cannot be empty', { category: requestedCategory });
    }

    // Check if category exists
    const availableCategories = cacheManager.getAvailableCategories();
    if (!availableCategories.includes(requestedCategory)) {
      throw new ValidationError(`Invalid category: ${requestedCategory}. Available categories: ${availableCategories.join(', ')}`, {
        category: requestedCategory,
        availableCategories
      });
    }

    try {
      // Get category-specific feed directly from cache manager
      const categoryFeed = cacheManager.getCategory(requestedCategory);

      // Check if category has any data
      if (categoryFeed.items.length === 0 && !categoryFeed.lastUpdated) {
        return reply.status(503).send({
          message: `Feed data for category '${requestedCategory}' is not yet available. Please try again later.`,
          details: "Initial scrape might be in progress or has not run yet."
        });
      }

      return categoryFeed;
    } catch (error) {
      throw new ValidationError(`Failed to retrieve category feed: ${error.message}`, { category: requestedCategory });
    }
  } else {
    // If there is no category parameter, return the aggregated feed from all categories
    const aggregatedFeed = cacheManager.getAggregatedFeed();

    // Check if any data is available
    if (aggregatedFeed.items.length === 0 && !aggregatedFeed.lastUpdated) {
      return reply.status(503).send({
        message: "Feed data is not yet available. Please try again later.",
        details: "Initial scrape might be in progress or has not run yet."
      });
    }

    return aggregatedFeed;
  }
};

export const getFeed = withErrorHandling(getFeedHandler);
